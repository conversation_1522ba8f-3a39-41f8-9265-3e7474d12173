import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServer } from '@/lib/supabase/server';
import { tableNames } from '@/constants/table_names';

export async function GET(request: NextRequest) {
  const supabase = createSupabaseServer();
  const { searchParams } = new URL(request.url);
  const orgId = searchParams.get('organization_id');
  const slpId = searchParams.get('slp_id');

  if (!orgId || !slpId) {
    return NextResponse.json(
      { message: 'organization_id and slp_id are required' },
      { status: 400 }
    );
  }

  const organization_id = Number(orgId);
  const slp_id = Number(slpId);

  // Query invoices - filter for pending statuses only
  const { data: pendingInvoices, error: pendingError } = await supabase
    .from(tableNames.invoices)
    .select('id, total_price, status')
    .eq('organization_id', organization_id)
    .eq('slp_id', slp_id)
    .in('status', ['AWAITING_PAYMENT', 'PARTIALLY_PAID']);

  if (pendingError) {
    return NextResponse.json(
      { message: pendingError.message },
      { status: 500 }
    );
  }

  // Query invoices for revenue - filter for paid statuses
  const { data: revenueInvoices, error: revenueError } = await supabase
    .from(tableNames.invoices)
    .select('id, total_price, status')
    .eq('organization_id', organization_id)
    .eq('slp_id', slp_id)
    .in('status', ['PARTIALLY_PAID', 'PAID']);

  if (revenueError) {
    return NextResponse.json(
      { message: revenueError.message },
      { status: 500 }
    );
  }

  // Compute pending totals
  const pendingTotalAmount = pendingInvoices.reduce(
    (sum, invoice) => sum + (invoice.total_price || 0),
    0
  );

  // Compute revenue totals
  const revenueTotalAmount = revenueInvoices.reduce(
    (sum, invoice) => sum + (invoice.total_price || 0),
    0
  );

  return NextResponse.json(
    {
      pending_count: pendingInvoices.length,
      pending_total_amount: Number(pendingTotalAmount.toFixed(2)),
      revenue_count: revenueInvoices.length,
      revenue_total_amount: Number(revenueTotalAmount.toFixed(2)),
    },
    { status: 200 }
  );
}
