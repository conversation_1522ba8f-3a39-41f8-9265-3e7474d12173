import { env } from '@/constants/env';
import { KlaviyoAPI } from '@/lib/klaviyo/api';
import { KlaviyoDatabase } from '@/lib/klaviyo/db';
import { KlaviyoOAuth } from '@/lib/klaviyo/oauth';
import { createClient } from '@supabase/supabase-js';
import { NextResponse } from 'next/server';

const supabaseAdmin = createClient(
  env.SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);
export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const code = searchParams.get('code');
  const state = searchParams.get('state');
  const error = searchParams.get('error');

  console.log('code is ', code);
  console.log('state is ', state);
  // console.log('error is ', error);
  // console.log('request.url is ', request.url);

  if (error) {
    return NextResponse.redirect(new URL(`/overview`, request.url));
  }

  if (!code || !state) {
    return NextResponse.redirect(new URL('/overview', request.url));
  }

  try {
    const { organization_id } = JSON.parse(
      Buffer.from(state, 'base64').toString()
    );
    // console.log('organization_id is ', organization_id);

    // Exchange code for tokens
    const tokens = await KlaviyoOAuth.exchangeCodeForTokens(
      code as string,
      organization_id,
      supabaseAdmin
    );

    // Get account information
    const klaviyo = new KlaviyoAPI(tokens.access_token);
    const accountInfo = await klaviyo.getAccount();
    // console.log('accountInfo is ', accountInfo);

    // Save integration to database
    await KlaviyoDatabase.saveIntegration(
      organization_id,
      tokens,
      accountInfo,
      supabaseAdmin
    );

    return NextResponse.redirect(
      new URL(`/profile?tab=account-settings`, request.url)
    );
  } catch (error) {
    console.error('Error handling Klaviyo callback:', error);
    return NextResponse.redirect(
      new URL('/therapist/profile?klaviyo=error', request.url)
    );
  }
}
