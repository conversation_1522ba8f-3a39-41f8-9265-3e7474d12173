'use server';
import { NextResponse } from 'next/server';
import { tableNames } from '@/constants/table_names';
import { createSupabaseServer } from '@/lib/supabase/server';
import moment from 'moment';

export async function POST(request: Request) {
  const supabase = createSupabaseServer();

  try {
    const body = await request.json();
    const {
      // Booking data
      booking_id, // If exists, it's an existing booking
      client_id,
      service_id,
      assigned_to,
      appointment,
      event,
      organization_id,
      user_id,
      invoice_id,

      // Purchase/Invoice data
      purchaseData, // Contains service, price, etc.
    } = body;

    if (!client_id || !service_id || !organization_id || !user_id) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    let bookingResult;
    let invoiceResult;
    let slpNotesResult;

    // Start transaction-like operations
    if (!booking_id) {
      // CREATE NEW BOOKING FLOW
      console.log('Creating new booking...');

      // 1. Create new booking
      const { data: newBooking, error: bookingError } = await supabase
        .from(tableNames.bookings)
        .insert({
          client_id: Number(client_id),
          service_id: Number(service_id),
          assigned_to,
          appointment,
          event,
          slp_id: Number(user_id),
          product_id: Number(service_id), // Assuming service_id maps to product_id
        })
        .select('*')
        .single();

      if (bookingError) {
        throw new Error(`Failed to create booking: ${bookingError.message}`);
      }
      bookingResult = newBooking;

      // 3. Create slp_notes linking booking and invoice
      const { data: slpNotes, error: slpNotesError } = await supabase
        .from(tableNames.slp_notes)
        .insert({
          booking_id: newBooking.id,
          client_id: Number(client_id),
          invoice_id: invoice_id,
          slp_id: Number(user_id),
          status: 'Created',
          soap_note: 'Booking Confirmed',
          note_date: moment().format('YYYY-MM-DD'),
        })
        .select('*')
        .single();

      if (slpNotesError) {
        throw new Error(`Failed to create SLP notes: ${slpNotesError.message}`);
      }
      slpNotesResult = slpNotes;
    } else {
      // EXISTING BOOKING FLOW
      console.log('Using existing booking...');

      // 1. Get existing booking
      const { data: existingBooking, error: fetchError } = await supabase
        .from(tableNames.bookings)
        .select('*, slp_notes(*)')
        .eq('id', booking_id)
        .single();

      if (fetchError) {
        throw new Error(`Failed to fetch booking: ${fetchError.message}`);
      }
      bookingResult = existingBooking;

      // 3. Handle slp_notes
      if (existingBooking.slp_notes) {
        // Update existing slp_notes with invoice_id
        const { data: updatedSlpNotes, error: updateError } = await supabase
          .from(tableNames.slp_notes)
          .update({ invoice_id: invoice_id })
          .eq('booking_id', booking_id)
          .select('*')
          .single();

        if (updateError) {
          throw new Error(`Failed to update SLP notes: ${updateError.message}`);
        }
        slpNotesResult = updatedSlpNotes;
      } else {
        // Create new slp_notes
        const { data: newSlpNotes, error: createError } = await supabase
          .from(tableNames.slp_notes)
          .insert({
            booking_id: Number(booking_id),
            client_id: Number(client_id),
            invoice_id: invoice_id,
            slp_id: Number(user_id),
            status: 'Created',
            soap_note: 'Booking Confirmed',
            note_date: moment().format('YYYY-MM-DD'),
          })
          .select('*')
          .single();

        if (createError) {
          throw new Error(`Failed to create SLP notes: ${createError.message}`);
        }
        slpNotesResult = newSlpNotes;
      }
    }

    // 4. Update services_purchases.slp_note_id && booking_id if purchaseData contains purchase info
    if (purchaseData?.id) {
      const { error: purchaseUpdateError } = await supabase
        .from(tableNames.services_purchases)
        .update({
          // slp_note_id: slpNotesResult?.id,
          booking_id: bookingResult.id,
          status: 'REDEEMED',
        })
        .eq('id', purchaseData.id);

      if (purchaseUpdateError) {
        console.warn(
          `Failed to update services_purchases: ${purchaseUpdateError.message}`
        );
        // Don't throw error here as the main operation succeeded
      }
    }

    return NextResponse.json(
      {
        success: true,
        data: {
          booking_id: bookingResult.id,
          invoice_id: invoice_id,
          slp_notes_id: slpNotesResult.id,
          booking: bookingResult,
          invoice: invoiceResult,
          slp_notes: slpNotesResult,
          slp_id: user_id,
        },
      },
      { status: 200 }
    );
  } catch (error: any) {
    console.error('Error in link-booking endpoint:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to process booking' },
      { status: 500 }
    );
  }
}
