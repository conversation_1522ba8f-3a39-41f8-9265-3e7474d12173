import { tableNames } from '@/constants/table_names';
import { createSupabaseServer } from '@/lib/supabase/server';
import 'moment-timezone';
import 'moment/locale/en-ca';
import moment from 'moment/moment';
import { NextResponse } from 'next/server';

export async function GET(request: Request) {
  try {
    const supabase = createSupabaseServer();
    const { searchParams } = new URL(request.url);

    const slpId = searchParams.get('slpId') || '';
    const organization_id = searchParams.get('organization_id') || '';
    const date = searchParams.get('date') || '';
    const userTimeZone = searchParams.get('timezone') || 'Canada/Eastern';

    const start = moment
      .tz(date, userTimeZone)
      .startOf('day')
      .utc()
      .toISOString();
    const end = moment.tz(date, userTimeZone).endOf('day').utc().toISOString();

    const { data } = await supabase
      .from(tableNames.bookings)
      .select(
        `*, 
      clients(*,  packages(*), invoices(*) , email_activities(*)), 
      slp_notes(*,
      invoice:invoices!slp_notes_invoice_id_fkey(*,    
      slp_data:users(*),
      services(*),
      purchased_package:invoices_purchased_package_id_fkey(*,
      purchased_package_items (
        id,
        quantity,
        remaining,
        service:service_id (*)
      ),
      package_offering(*)
      ),
      transactions!transactions_invoice_id_fkey(*),
      redeemed_sessions!redeemed_sessions_invoice_id_fkey(*)
      )
      )
      
)`
        // package_used:redeemed_sessions(*),
      )
      .eq('slp_id', slpId)
      .eq('organization_id', organization_id)
      .gte('appointment', start)
      .lte('appointment', end)
      .order('appointment');

    // Exclude only canceled events (NULL values will be included)
    // .or(
    //   `calendly_event_type.is.null,calendly_event_type.neq.invitee.canceled`
    // )

    console.log('data---4', data);

    return NextResponse.json({ success: true, data });
  } catch (error: any) {
    return NextResponse.json(
      { success: false, message: error.message || 'Something went wrong' },
      { status: 500 }
    );
  }
}
