'use client';
import React, { useState } from 'react';
import {
  Box,
  Flex,
  Text,
  Button,
  Center,
  Heading,
  Badge,
  Stack,
} from '@chakra-ui/react';

import { FiPlus, FiEdit2, FiTrash2 } from 'react-icons/fi';
import { useSearchParams } from 'next/navigation';

import { useGetAllTaxesQuery } from '@/api/newsf/queries';
import { useSupabaseSession } from '@/hooks/auth/useUserSession';
import AnimateLoader from '@/components/elements/loader/animate-loader';
import { CustomModal } from '@/components/elements/modal/custom-modal';
import TaxForm from '@/app/(dashboard)/create-invoice/_components/TaxForm';

const Taxes = () => {
  const { UserFromQuery } = useSupabaseSession();
  const searchParams = useSearchParams();
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);

  // Get filter parameters
  const orgIdFromUrl = searchParams.get('organization_id');
  const userIdFromUrl = searchParams.get('user_id');

  const filter = {
    user_id: userIdFromUrl || UserFromQuery?.id || '',
    organization_id: orgIdFromUrl || UserFromQuery?.organization?.id,
  };

  console.log('Filter for taxes:', filter);
  console.log('UserFromQuery:', UserFromQuery);

  const {
    data: taxData,
    isLoading,
    refetch,
  } = useGetAllTaxesQuery(
    {
      user_id: userIdFromUrl || UserFromQuery?.id || '',
      org_id: orgIdFromUrl || UserFromQuery?.organization?.id,
    },
    {
      enabled: !!userIdFromUrl || !!UserFromQuery?.organization_id,
      generateItemId: true,
    }
  );

  const handleCreateSuccess = () => {
    refetch();
    setIsCreateModalOpen(false);
  };

  return (
    <Box>
      {/* Header */}
      <Flex justify="space-between" align="center" mb={6}>
        <Box>
          <Heading size="lg" color="gray.900">
            Tax Management
          </Heading>
          <Text color="gray.600" mt={1}>
            Manage your tax rates and settings
          </Text>
        </Box>
        <Button
          bg="#e97a5b"
          color="white"
          _hover={{ bg: '#d16a4a' }}
          onClick={() => setIsCreateModalOpen(true)}
        >
          <FiPlus style={{ marginRight: '8px' }} />
          Add New Tax
        </Button>
      </Flex>

      {/* Tax Cards Display */}
      <Box minH="500px">
        {isLoading ? (
          <Center h="500px">
            <AnimateLoader />
          </Center>
        ) : taxData?.data && taxData.data.length > 0 ? (
          <Stack gap={4}>
            {taxData.data.map((tax: any, index: number) => (
              <Box
                key={tax.id || index}
                border="1px solid"
                borderColor="gray.200"
                borderRadius="lg"
                p={6}
                bg="white"
                shadow="sm"
                _hover={{
                  shadow: 'md',
                  borderColor: 'gray.300',
                  transform: 'translateY(-1px)',
                }}
                transition="all 0.2s ease"
              >
                <Flex justify="space-between" align="center">
                  {/* Left Section - Tax Info */}
                  <Flex direction="column" flex="1" gap={2}>
                    <Flex align="center" gap={4}>
                      <Text
                        fontSize="lg"
                        fontWeight="semibold"
                        color="gray.900"
                      >
                        {tax.name}
                      </Text>
                      <Badge
                        colorScheme={tax.is_active ? 'green' : 'red'}
                        variant="subtle"
                        fontSize="xs"
                        px={2}
                        py={1}
                        borderRadius="full"
                      >
                        {tax.is_active ? 'Active' : 'Inactive'}
                      </Badge>
                    </Flex>

                    {tax.description && (
                      <Text fontSize="sm" color="gray.600" maxW="400px">
                        {tax.description}
                      </Text>
                    )}

                    <Text fontSize="xs" color="gray.500">
                      Created: {new Date(tax.created_at).toLocaleDateString()}
                    </Text>
                  </Flex>

                  {/* Center Section - Tax Rate */}
                  <Box textAlign="center" px={6}>
                    <Text fontSize="2xl" fontWeight="bold" color="#e97a5b">
                      {tax.value}%
                    </Text>
                    <Text
                      fontSize="xs"
                      color="gray.500"
                      textTransform="uppercase"
                    >
                      Tax Rate
                    </Text>
                  </Box>

                  {/* Right Section - Actions */}
                  <Flex gap={2} align="center">
                    <Button
                      size="sm"
                      variant="ghost"
                      colorScheme="blue"
                      p={2}
                      minW="auto"
                      h="auto"
                      onClick={(e) => {
                        e.stopPropagation();
                        // TODO: Implement edit functionality
                        console.log('Edit tax:', tax);
                      }}
                      _hover={{
                        bg: 'blue.50',
                        color: 'blue.600',
                      }}
                    >
                      <FiEdit2 size={16} />
                    </Button>
                    <Button
                      size="sm"
                      variant="ghost"
                      colorScheme="red"
                      p={2}
                      minW="auto"
                      h="auto"
                      onClick={(e) => {
                        e.stopPropagation();
                        // TODO: Implement delete functionality
                        console.log('Delete tax:', tax);
                      }}
                      _hover={{
                        bg: 'red.50',
                        color: 'red.600',
                      }}
                    >
                      <FiTrash2 size={16} />
                    </Button>
                  </Flex>
                </Flex>
              </Box>
            ))}
          </Stack>
        ) : (
          <Center h="400px" flexDirection="column" gap={4}>
            <Box textAlign="center">
              <Text fontSize="lg" fontWeight="medium" color="gray.500" mb={2}>
                No taxes found
              </Text>
              <Text fontSize="sm" color="gray.400" mb={6}>
                Create your first tax to get started
              </Text>
              <Button
                bg="#e97a5b"
                color="white"
                _hover={{ bg: '#d16a4a' }}
                onClick={() => setIsCreateModalOpen(true)}
                size="lg"
              >
                <FiPlus style={{ marginRight: '8px' }} />
                Add Your First Tax
              </Button>
            </Box>
          </Center>
        )}
      </Box>

      {/* Create Tax Modal */}
      <CustomModal
        open={isCreateModalOpen}
        onOpenChange={(details) => setIsCreateModalOpen(details.open)}
        headertext="Create New Tax"
        size="md"
      >
        <TaxForm
          onClose={() => setIsCreateModalOpen(false)}
          onSuccess={handleCreateSuccess}
        />
      </CustomModal>
    </Box>
  );
};

export default Taxes;
