import { toaster } from '@/components/ui/toaster';
import { queryKey } from '@/constants/query-key';
import { tableNames } from '@/constants/table_names';
import { ToastMessages } from '@/constants/toast-messages';
import supabase from '@/lib/supabase/client';
import { useQueryClient } from '@tanstack/react-query';

import { useFormik } from 'formik';
import moment from 'moment';
import { useState } from 'react';

export const useEditTransactionHook = (
  transaction: any,
  onClose: () => void
) => {
  const [loading, setLoading] = useState(false);
  const queryClient = useQueryClient();

  const editTransactionInitialValues = {
    transaction_date: transaction?.transaction_date ?? null,
    name:
      transaction?.name ||
      transaction?.client?.first_name + ' ' + transaction?.client?.last_name,
    amount: transaction?.amount || '',
    payment_method: transaction?.payment_method || '',
    note: transaction?.note || '',
    transaction_type: transaction?.transaction_type || '',
  };

  // const formatDate = (dateStr: any) => {
  //   // Handle ISO string format (e.g., "2024-11-13T23:00:00Z")
  //   if (dateStr.includes('T')) {
  //     return dateStr.split('T')[0];
  //   }

  //   // Handle "YYYY-MM-DD HH:mm:ss" format
  //   if (dateStr.includes(' ')) {
  //     return dateStr.split(' ')[0];
  //   }

  //   // Return the original string if it doesn't match the expected formats
  //   return dateStr;
  // };

  const {
    values,
    handleSubmit,
    errors,
    touched,
    handleChange,
    setFieldValue,
    handleBlur,
  } = useFormik({
    initialValues: editTransactionInitialValues,
    onSubmit: async (values) => {
      //   console.log('values is ', values);
      try {
        setLoading(true);

        if (!values?.amount) {
          throw new Error('Please select amount');
        }

        const insert = {
          transaction_date: moment(values?.transaction_date).format(
            'YYYY-MM-DD'
          ),

          // transaction_date: moment(values?.transaction_date)
          //   .utc()
          //   .format('YYYY-MM-DDTHH:mm:ss[Z]'),
          organization_id: transaction?.organization_id,
          user_id: transaction?.user_id,
          amount: values?.amount,
          payment_method: values?.payment_method,
          note: values?.note,
          transaction_type: values?.transaction_type,
        };

        console.log(`insert`, insert);

        const { error } = await supabase
          .from(tableNames.transactions)
          .update(insert)
          .eq('id', transaction?.id as string);
        if (error) throw error;

        toaster.create({
          description: 'Transaction  Updated Successfully ',
          type: 'success',
        });

        await queryClient.invalidateQueries({
          queryKey: [queryKey.transactions.getAll],
        });
        onClose();
      } catch (error: any) {
        setLoading(false);

        toaster.create({
          description: error?.message || ToastMessages.somethingWrong,
        });
      } finally {
        setLoading(false);
      }
    },
  });

  return {
    handleSubmit,
    values,
    errors,
    touched,
    handleChange,
    loading,
    setFieldValue,
    handleBlur,
    // formatDate,
  };
};
