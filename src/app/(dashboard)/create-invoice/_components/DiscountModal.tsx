'use client';

import React from 'react';
import { CustomModal } from '@/components/elements/modal/custom-modal';
import { Box, Text, Stack, HStack } from '@chakra-ui/react';
import { Button } from '@/components/ui/button';
import { Radio, RadioGroup } from '@/components/ui/radio';
import StringInput from '@/components/Input/StringInput';
import { useFormik } from 'formik';
import * as Yup from 'yup';

interface DiscountModalProps {
  isOpen: boolean;
  onClose: () => void;
  onApplyDiscount: (discount: {
    type: 'percentage' | 'currency';
    value: number;
  }) => void;
  currentDiscount?: { type: 'percentage' | 'currency'; value: number };
}

const validationSchema = Yup.object({
  value: Yup.number()
    .required('Discount value is required')
    .min(0, 'Discount value must be positive'),
  type: Yup.string()
    .oneOf(['percentage', 'currency'], 'Invalid discount type')
    .required('Discount type is required'),
});

export const DiscountModal = ({
  isOpen,
  onClose,
  onApplyDiscount,
  currentDiscount,
}: DiscountModalProps) => {
  const formik = useFormik({
    initialValues: {
      value: currentDiscount?.value || 0,
      type: currentDiscount?.type || 'percentage',
    },
    validationSchema,
    enableReinitialize: true,
    onSubmit: (values) => {
      onApplyDiscount({
        type: values.type as 'percentage' | 'currency',
        value: Number(values.value),
      });
      onClose();
    },
  });

  const handleClose = () => {
    formik.resetForm();
    onClose();
  };

  // Debug: Log current formik values

  const handleRemoveDiscount = () => {
    onApplyDiscount({ type: 'percentage', value: 0 });
    onClose();
  };

  return (
    <CustomModal
      open={isOpen}
      onOpenChange={handleClose}
      headertext="Add Discount"
      w={{ base: '90%', md: '400px' }}
    >
      <form onSubmit={formik.handleSubmit}>
        <Stack gap={4}>
          <Box>
            <Text fontSize="sm" fontWeight="medium" mb={2}>
              Discount Type
            </Text>
            <RadioGroup
              value={formik.values.type}
              onValueChange={(details) => {
                console.log('Radio value changed:', details);
                const newType = details.value || details;
                console.log('Setting type to:', newType);
                formik.setFieldValue('type', newType);
              }}
            >
              <HStack gap={6}>
                <Radio value="percentage">
                  <Text fontSize="sm">Percentage (%)</Text>
                </Radio>
                <Radio value="currency">
                  <Text fontSize="sm">Fixed Amount ($)</Text>
                </Radio>
              </HStack>
            </RadioGroup>
          </Box>

          <StringInput
            fieldProps={{
              label: 'Discount Value',
              invalid: !!(formik.touched.value && formik.errors.value),
              errorText: formik.touched.value ? formik.errors.value : undefined,
            }}
            inputProps={{
              type: 'number',
              step: '0.01',
              min: '0',
              max: formik.values.type === 'percentage' ? '100' : undefined,
              placeholder:
                formik.values.type === 'percentage'
                  ? 'Enter percentage (0-100)'
                  : 'Enter amount',
              name: 'value',
              value: formik.values.value,
              onChange: (e: React.ChangeEvent<HTMLInputElement>) =>
                formik.setFieldValue('value', e.target.value),
              onBlur: formik.handleBlur,
            }}
          />

          <Box>
            <Text fontSize="sm" color="gray.600">
              {formik.values.type === 'percentage'
                ? `${formik.values.value}% discount will be applied`
                : `$${formik.values.value} will be deducted from the total`}
            </Text>
          </Box>

          <HStack gap={3} justifyContent="flex-end" mt={4}>
            {currentDiscount && currentDiscount.value > 0 && (
              <Button
                type="button"
                variant="outline"
                colorScheme="red"
                onClick={handleRemoveDiscount}
              >
                Remove Discount
              </Button>
            )}
            <Button type="button" variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            <Button
              type="submit"
              bg="#e97a5b"
              color="white"
              disabled={!formik.isValid || !formik.dirty}
            >
              Apply Discount
            </Button>
          </HStack>
        </Stack>
      </form>
    </CustomModal>
  );
};
