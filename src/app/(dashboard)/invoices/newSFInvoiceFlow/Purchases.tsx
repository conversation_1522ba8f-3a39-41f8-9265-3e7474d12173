import CustomTable from '@/components/table/CustomTable';
import { Box, Center, Flex, Text } from '@chakra-ui/react';
import React from 'react';
import { IoWalletOutline } from 'react-icons/io5';
import { createColumnDef } from './column-def';

export default function Purchases({ invoice }: { invoice: any }) {
  return (
    <Box
      border={'1px solid #FEFEFE'}
      boxShadow={'lg'}
      rounded={'12px'}
      py={'1.5rem'}
      px={'10px'}
      w={'full'}
      minH={'6rem'}
      position={'relative'}
      maxH={'fit-content'}
    >
      <Flex width={'full'} gap={'1.25rem'} overflow={'hidden'}>
        <Center
          rounded={'full'}
          fontSize={'18px'}
          boxSize={'36px'}
          cursor={'pointer'}
          color={'#E97A5B'}
          border={'2px solid #E97A5B'}
        >
          <IoWalletOutline />
        </Center>

        <Box overflow={'hidden'} width={'full'} maxWidth={'full'}>
          <Text mb={'1rem'} color={'GrayText'}>
            Manage Purchases
          </Text>

          <CustomTable
            columnDef={createColumnDef(invoice)}
            data={invoice?.services_purchases || []}
            minHeight={'2rem'}
          />
        </Box>
      </Flex>
    </Box>
  );
}
