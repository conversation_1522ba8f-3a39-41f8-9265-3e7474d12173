import { usePendingInvoicesStatsQuery } from '@/api/invoices/get-not-paid-invoices';
import { useDashboardStatsQuery } from '@/api/overview/get-avearage-ltv';
import CustomDatePicker from '@/components/elements/date-picker/date-picker';
import AnimateLoader from '@/components/elements/loader/animate-loader';
import { Button } from '@/components/ui/button';
import { Colors } from '@/constants/colors';
import { permissions } from '@/constants/permissions';
import { useSlpDashboard } from '@/hooks/slp/useDashboard';
import {
  Box,
  Center,
  chakra,
  Flex,
  GridItem,
  Heading,
  HStack,
  Icon,
  SimpleGrid,
  Span,
  Stack,
  Text,
  VStack,
} from '@chakra-ui/react';
import moment from 'moment';
import Link from 'next/link';
import { forwardRef, useMemo } from 'react';
import { BsCurrencyDollar } from 'react-icons/bs';
import { FaRegChartBar } from 'react-icons/fa';
import { FiCheckCircle } from 'react-icons/fi';
import { IoMdTime } from 'react-icons/io';
import { LiaTimesSolid } from 'react-icons/lia';
import { LuCalendar, LuCreditCard, LuPlus, LuUsers } from 'react-icons/lu';
import { MdDone } from 'react-icons/md';
import { RiMoneyDollarCircleLine } from 'react-icons/ri';
import { AddTask } from './AddTask';

type CustomInputProps = {
  onClick?: () => void;
  selectDate?: any;
  handleDateChange?: any;
};

// Custom Date Picker Trigger
export const IconInput = forwardRef<HTMLButtonElement, CustomInputProps>(
  ({ onClick, selectDate, handleDateChange }, ref) => {
    // Helper function to get display text for the date
    const getDateDisplay = (date?: Date | string | null) => {
      const effectiveDate = date && moment(date).isValid() ? date : moment();
      const selectDateFormatted = moment(effectiveDate).format('YYYY-MM-DD');
      const today = moment().format('YYYY-MM-DD');
      const yesterday = moment().subtract(1, 'day').format('YYYY-MM-DD');
      const tomorrow = moment().add(1, 'day').format('YYYY-MM-DD');

      if (selectDateFormatted === today) return 'Today';
      if (selectDateFormatted === yesterday) return 'Yesterday';
      if (selectDateFormatted === tomorrow) return 'Tomorrow';
      return selectDateFormatted; // Fallback to YYYY-MM-DD
    };

    return (
      <Box
        display={'flex'}
        gap={'2'}
        alignItems={'center'}
        border="1px solid"
        borderColor={'gray.50'}
        rounded={'md'}
        py={'1.5'}
        px={'2'}
      >
        <button
          style={{
            cursor: 'pointer',
            display: 'flex',
            alignItems: 'center',
          }}
          onClick={onClick}
          ref={ref}
          title="Select Date"
          type="button"
        >
          <LuCalendar size={17} />
          <Text fontSize="md" ml={'3'} fontWeight={'500'}>
            {getDateDisplay(selectDate)}
          </Text>
        </button>
        {selectDate &&
          moment(selectDate).isValid() &&
          moment(selectDate).format('YYYY-MM-DD') !==
            moment().format('YYYY-MM-DD') && (
            <button
              style={{
                cursor: 'pointer',
                background: '#e7e7e7',
                padding: '0 10px',
                borderRadius: '10px',
                display: 'flex',
                alignItems: 'center',
                gap: '2px',
              }}
              onClick={() => {
                if (handleDateChange) {
                  const newDate = new Date();
                  handleDateChange(newDate, true);
                }
              }}
              title="Clear Date"
              type="button"
            >
              <LiaTimesSolid size={12} />
              Clear
            </button>
          )}
      </Box>
    );
  }
);

IconInput.displayName = 'IconInput';

export function Dashboard({ slp }: any) {
  const hook = useSlpDashboard({ slp });
  const { data: averageLtvData } = useDashboardStatsQuery(slp);
  const { data: pendingInvoicesAmountData } = usePendingInvoicesStatsQuery(slp);

  const {
    getCurrentCoachingHour,
    //getCurrentInvoice,
    abbr,
    SlpCalendarSessions,
    SlpSessions,
    showTodo,
    handleUpdateTodo,
    //GIapi,
    handleTabChange,
    isAdmin,
    SlpViewData,
    RPCData,
    repeatRate,
    hasPermission,
    paySchedules,
    todoHook,
    handleDateChange,
    selectDate,
    SlpSessionFetching,
    // setSelectDate,
    highlightDates,
  } = hook;

  //const payAmount = getCurrentInvoice(GIapi || [])?.currentPay;
  // console.log('GIapi', GIapi);

  const PackageBalanceComp = ({ data }: { data: any }) => {
    // console.log('data', data?.slp_notes);
    if (data?.slp_notes?.invoice?.purchased_package?.purchased_package_items) {
      // console.log('hello');
      const packagesItems =
        data?.slp_notes?.invoice?.purchased_package?.purchased_package_items;
      // Calculate totals
      const totalQuantity = packagesItems.reduce(
        (sum: any, item: any) => sum + item.quantity,
        0
      );
      const totalRemaining = packagesItems.reduce(
        (sum: any, item: any) => sum + item.remaining,
        0
      );
      const totalUsed = totalQuantity - totalRemaining;
      // console.log('totalUsed', totalUsed);
      // console.log('totalQuantity', totalQuantity);

      if (totalUsed === totalQuantity) {
        return (
          <Text
            fontSize={'11px'}
            bg={Colors?.RED?.PRIMARY}
            color={Colors?.RED?.LIGHT}
            borderRadius={'10px'}
            px={3}
            py={'3px'}
          >
            No Active Package
          </Text>
        );
      } else {
        return (
          <Flex
            justifyContent={'flex-start'}
            fontSize={'11px'}
            alignItems={'center'}
            gap={1}
          >
            <Text fontSize={'11px'}>Active Package Balance : </Text>
            <chakra.span color="primary.500" fontWeight="bold">
              {totalUsed}
            </chakra.span>
            /<chakra.span fontWeight="bold">{totalQuantity}</chakra.span>
          </Flex>
        );
      }
    }
    // console.log('casca--5');
    return null;
  };

  const SpeakFluentPackageComp = ({ data }: { data: any }) => {
    // Don't show anything for Meeting/Interview events
    if (data && data?.event === 'Meeting/Interview') {
      return null;
    }
    // console.log('data', data);
    if (data && data?.event?.indexOf('ackage') > -1) {
      const inCompletePackages = data?.clients?.packages
        .filter(
          (item: any) =>
            item?.status?.toLowerCase() === 'incomplete' ||
            item?.status?.toLowerCase() === 'active' ||
            item?.status?.toLowerCase() === 'paid'
        )
        .sort(
          (a: any, b: any) =>
            new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
        );

      const currentIncompletePackage: any =
        inCompletePackages.length > 0
          ? inCompletePackages[0] // The oldest one
          : inCompletePackages[0];

      // console.log('currentIncompletePackage', currentIncompletePackage);
      // console.log('inCompletePackages', inCompletePackages);

      const packageSize = currentIncompletePackage?.package_size || 0;
      const balance = currentIncompletePackage?.balance || 0;
      const used = Math.max(0, packageSize - balance);

      // console.log('packageSize---1', packageSize);
      // console.log('balance---2', balance);
      // console.log('used---3', used);

      if (inCompletePackages?.length === 0) {
        return (
          <Text
            borderRadius={'10px'}
            px={3}
            py={'3px'}
            fontSize={'11px'}
            bg={Colors?.RED?.PRIMARY}
            color={Colors?.RED?.LIGHT}
          >
            No Active Package
          </Text>
        );
      } else {
        return (
          <Flex
            justifyContent={'flex-end'}
            fontSize={'11px'}
            alignItems={'center'}
            gap={1}
            py={1}
            px={2}
            borderRadius={'10px'}
            bg={Colors?.GREEN?.PRIMARY}
            color={'white'}
          >
            <Text fontSize={'11px'}>Active Package Balance : </Text>
            <chakra.span fontWeight="bold">{used}</chakra.span>/
            <chakra.span fontWeight="bold">{packageSize}</chakra.span>
          </Flex>
        );
      }
    } else if (data && data?.event?.indexOf('Assessment') > -1) {
      return (
        <Text
          borderRadius={'10px'}
          px={3}
          py={'3px'}
          fontSize={'11px'}
          bg={Colors?.GREEN?.PRIMARY}
          color={'white'}
        >
          Paid
        </Text>
      );
    } else {
      return (
        <Text
          fontSize={'11px'}
          borderRadius={'10px'}
          px={3}
          py={'3px'}
          bg={Colors?.GREEN?.PRIMARY}
          color={'white'}
        >
          Paid
        </Text>
      );
    }
  };

  // console.log('SlpViewData', SlpViewData);

  const topList = [
    {
      id: 1,
      title: 'Total Clients',
      value: SlpViewData?.pagination?.total_client_count || 0,
      note: '',
      // note: `${clientsPercentage} this month`,
      icon: LuUsers,
      iconColor: 'blue.500',
      onClick: () => handleTabChange('clients'),
      canView: isAdmin || hasPermission(permissions.canViewBillingInvoices),
    },
    {
      id: 2,
      title: 'Invoices Hours',
      value: `${getCurrentCoachingHour(paySchedules)?.currentHour}h`,
      note: '',
      // note: `${getCurrentCoachingHour(paySchedules)?.percentageChange || 0}% of monthly goal`,
      icon: IoMdTime,
      iconColor: 'purple.500',
      onClick: () => handleTabChange('sessions'),
      canView: isAdmin || hasPermission(permissions.canViewBillingInvoices),
    },
    {
      id: 3,
      title: 'Revenue',
      // value: payAmount
      //   ? new Intl.NumberFormat('en-US', {
      //       style: 'currency',
      //       currency: 'USD',
      //       minimumFractionDigits: 2,
      //       maximumFractionDigits: 2,
      //     }).format(Number(payAmount))
      //   : '$0.00',
      value: new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      }).format(pendingInvoicesAmountData?.revenue_total_amount ?? 0),

      note: '',
      // note: `${getCurrentInvoice(GIapi)?.percentageChange}% from last month`,
      icon: BsCurrencyDollar,
      iconColor: 'green.500',
      onClick: () => handleTabChange('invoices'),
      canView: hasPermission(permissions.canViewAdmin),
    },
    {
      id: 4,
      title: 'Pending Invoices',
      value: new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      }).format(pendingInvoicesAmountData?.pending_total_amount ?? 0),
      note: `${pendingInvoicesAmountData?.pending_count ?? 0} invoices pending`,
      icon: LuCreditCard,
      iconColor: 'orange.500',
      onClick: () => handleTabChange('invoices'),
      canView: isAdmin || hasPermission(permissions.canViewBillingInvoices),
    },
  ];

  const analyticsList = [
    {
      id: 1,
      title: 'Active Clients',
      value: SlpViewData?.pagination?.active_client_count || 0,
      note: '+2 this month',
      icon: LuUsers,
    },
    {
      id: 2,
      title: 'Total Clients',
      value: `${SlpViewData?.pagination?.total_client_count || 0}`,
      note: ``,
      icon: IoMdTime,
    },
    {
      id: 3,
      title: 'Repeat Rate',
      value:
        RPCData?.repeat_percentage && RPCData?.repeat_percentage !== '0.00%'
          ? RPCData.repeat_percentage
          : (() => {
              const rate = repeatRate?.split(' ')[0];
              return isNaN(Number(rate?.replace('%', '')))
                ? '0%'
                : rate || '0%';
            })(),
      note: ``,
      icon: BsCurrencyDollar,
    },

    {
      id: 4,
      title: 'Average LTV',
      value: new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      }).format(averageLtvData?.average_ltv ?? 0),
      note: ``,
      icon: RiMoneyDollarCircleLine,
    },
    {
      id: 5,
      title: 'Avg Invoices per Client',
      value: `${averageLtvData?.average_invoice_count ?? 0}`,
      note: ``,
      icon: RiMoneyDollarCircleLine,
    },
  ];

  const checkDate = (date: any) => {
    const now = moment();
    if (!date) {
      return 'present';
    } else if (date?.isBefore(now, 'day')) {
      return 'past';
    } else if (date?.isSame(now, 'day')) {
      return 'present';
    } else {
      return 'future';
    }
  };
  const sessions = selectDate ? SlpSessions : SlpCalendarSessions?.next5Data;

  // console.log('session ', sessions);
  const infoText = useMemo(() => {
    const timeStatus = checkDate(selectDate);
    if (['past'].includes(timeStatus)) {
      return (
        <>
          {' '}
          You had{' '}
          <Span fontWeight={'bold'} fontSize={'lg'}>
            {sessions?.length || 0}
          </Span>{' '}
          session(s) on {selectDate?.format('YYYY-MM-DD')}{' '}
        </>
      );
    }
    if (['present'].includes(timeStatus)) {
      return (
        <>
          You have{' '}
          <Span fontWeight={'bold'} fontSize={'lg'}>
            {sessions?.length || 0}
          </Span>{' '}
          upcoming session(s) for today
        </>
      );
    }
    if (['future'].includes(timeStatus)) {
      return (
        <>
          You have{' '}
          <Span fontWeight={'bold'} fontSize={'lg'}>
            {sessions?.length || 0}
          </Span>{' '}
          upcoming session(s) on {selectDate?.format('YYYY-MM-DD')}
        </>
      );
    }
  }, [sessions?.length, selectDate]);

  const filteredList = topList.filter((tab) => tab.canView);
  const columnCount = filteredList.length >= 4 ? 4 : filteredList.length;

  return (
    <Stack gap={'6'} pt={{ base: '2', lg: '5' }} pb={'20'}>
      <Box>
        <Heading
          fontSize={{ base: '1.3rem', md: '2rem' }}
          fontWeight={'semibold'}
          mb={{ base: '1', lg: '3' }}
        >
          Welcome back{slp?.first_name && `, ${slp?.first_name}`}
        </Heading>
        <Text
          fontSize={{ base: 'sm', md: 'md' }}
          color={'gray.300'}
          fontWeight={'500'}
        >
          Here&apos;s what&apos;s happening with your practice today
        </Text>
      </Box>

      <SimpleGrid
        columns={{ base: 1, md: 2, lg: columnCount }}
        maxW="full"
        gap={'6'}
        mt={'2'}
      >
        {topList
          ?.filter((tab) => tab.canView)
          ?.map((item) => (
            <Stack
              key={item.id}
              w={'full'}
              minH={'32'}
              border={'1px solid'}
              borderColor={'gray.50'}
              rounded={'12px'}
              px={{ base: '4', lg: '6' }}
              py={'3'}
              justifyContent={'center'}
              cursor={'pointer'}
              onClick={item.onClick}
              _hover={{
                boxShadow: 'lg',
                transition: 'all 0.2s ease-in-out',
              }}
            >
              <HStack justifyContent={'space-between'}>
                <Text fontSize={'0.9rem'} color={'gray.300'}>
                  {item.title}
                </Text>

                <Icon color={item.iconColor} size={'lg'} as={item.icon} />
              </HStack>
              <Text
                mt={1}
                fontSize={'2rem'}
                fontWeight={'bold'}
                color={'gray.900'}
              >
                {item.value}
              </Text>
              <Text fontSize={'xs'}>{item.note}</Text>
            </Stack>
          ))}
      </SimpleGrid>

      <SimpleGrid columns={{ base: 2, lg: 5 }} gap={'6'} mt={'2'}>
        <GridItem colSpan={{ base: 2, lg: 3 }}>
          <Stack
            w={'full'}
            maxH={'40rem'}
            overflowY={'auto'}
            border={'1px solid'}
            borderColor={'gray.50'}
            rounded={'12px'}
            px={{ base: '4', lg: '6' }}
            py={'5'}
          >
            <Box
              display={'flex'}
              flexDirection={{ base: 'column', md: 'row' }}
              gap={'4'}
              justifyContent={'space-between'}
            >
              <VStack alignItems={'start'} gap={'0.5'} justifyContent={'start'}>
                {selectDate ? (
                  <Heading fontSize={'1.2rem'}>
                    Sessions for {selectDate?.format('MMMM D, YYYY')}
                  </Heading>
                ) : (
                  <Heading fontSize={'1.2rem'}>Upcoming Sessions</Heading>
                )}
                <Text
                  mt={'-1.5'}
                  fontSize={'0.8rem'}
                  fontWeight={'medium'}
                  color={'gray.300'}
                >
                  {infoText}
                </Text>
              </VStack>

              {/* {isLoading && <Text>Testing...</Text>} */}

              <Flex alignItems={'center'} gap={'2'}>
                <CustomDatePicker
                  onChange={handleDateChange}
                  defaultDate={selectDate}
                  timeZone={abbr?.zone}
                  highlightDates={highlightDates}
                  // onMonthYearChange={(year) => setCurrentYear(year)}
                  customInput={
                    <IconInput
                      selectDate={selectDate}
                      handleDateChange={handleDateChange}
                    />
                  }
                />
              </Flex>
            </Box>

            {SlpSessionFetching ? (
              <AnimateLoader />
            ) : (
              <Stack pt={'4'} mt={'5'} pb={'2'} gap={'4'}>
                {sessions?.length === 0 ? (
                  <Text textAlign={'center'} color={'gray.500'}>
                    No sessions available
                  </Text>
                ) : (
                  sessions?.map((item: any) => (
                    <Box
                      display={'flex'}
                      flexDirection={'column'}
                      border={'1px solid'}
                      borderColor={'gray.50'}
                      key={item.id}
                      p={'2'}
                      rounded={'10px'}
                      width={'100%'}
                    >
                      <Stack
                        flexDir={{ base: 'column', md: 'row' }}
                        justifyContent={'space-between'}
                        width={'100%'}
                        gap={'3'}
                      >
                        <HStack>
                          <Center
                            w={'9'}
                            h={'9'}
                            rounded={'100%'}
                            bg={'gray.50'}
                          >
                            {`${item?.clients?.first_name?.substring(0, 1) || ''}${item?.clients?.last_name?.substring(0, 1) || ''}`}
                          </Center>
                        </HStack>
                        <Stack width={'100%'} gap={'-5px'}>
                          <Flex
                            width={'100%'}
                            justifyContent={'space-between'}
                            alignItems={'center'}
                          >
                            <Text
                              fontWeight={'semibold'}
                              _hover={{ color: 'primary.300' }}
                            >
                              <Link
                                href={`/contacts/${item?.clients?.id}`}
                              >{`${item?.clients?.first_name || '-'} ${item?.clients?.last_name || ''}`}</Link>
                            </Text>

                            <Text fontSize={'sm'} fontWeight={'500'}>
                              {moment(item.appointment)
                                .tz(abbr?.zone)
                                .format('h:mm A')}
                              {item?.slp_notes?.duration &&
                                ` - ${moment(item.appointment)
                                  .tz(abbr?.zone)
                                  .add(item.slp_notes.duration, 'minutes')
                                  .format('h:mm A')}`}
                            </Text>

                            {/* <Text fontSize={'xs'}>
                              {`${moment(item.appointment)
                                .tz(abbr?.zone)
                                .calendar(null, {
                                  sameDay: ' h:mm A',
                                  lastDay: ' h:mm A',
                                  nextDay: ' h:mm A',
                                  lastWeek: 'h:mm A',
                                  sameElse: 'h:mm A',
                                })}`}
                            </Text> */}
                          </Flex>
                          {/* .calendar(null, {
                                    sameDay: '[Today at] h:mm A',
                                    lastDay: '[Yesterday at] h:mm A',
                                    nextDay: '[Tomorrow at] h:mm A',
                                    lastWeek: 'dddd [at] h:mm A',
                                    sameElse: 'MM/DD/YYYY [at] h:mm A',
                                  })}`} */}

                          <Flex
                            width={'100%'}
                            justifyContent={'space-between'}
                            alignItems={'center'}
                          >
                            <Text opacity={0.8} fontSize={'sm'} lineClamp={1}>
                              {/* {item.event || ''} */}
                              {item?.organization_id === 1
                                ? item?.event?.includes('ackage')
                                  ? item?.event
                                  : item?.event?.includes('Assessment')
                                    ? `${item?.event}[Assessment]`
                                    : item?.event === 'Meeting/Interview'
                                      ? item?.event
                                      : `${item?.event}[PAYG]`
                                : item?.event}
                            </Text>
                            {/* Hide invoice/payment UI for Meeting/Interview sessions */}
                            {item?.event !== 'Meeting/Interview' && (
                              <Flex
                                alignItems={'center'}
                                justifyContent={'flex-end'}
                              >
                                <Flex
                                  gap={2}
                                  alignItems={'center'}
                                  justifyContent={'flex-end'}
                                >
                                  {['invitee.created', null].includes(
                                    item?.calendly_event_type
                                  ) ? (
                                    <Flex
                                      justifyContent={'flex-end'}
                                      alignItems={'center'}
                                    >
                                      <Link
                                        href={`/slp/${slp.id}/create-invoice/${item.id}`}
                                      >
                                        <Center
                                          px={2}
                                          py={1}
                                          fontWeight="medium"
                                          rounded="md"
                                          fontSize={'sm'}
                                          transition="all 0.2s ease"
                                          border={'1px solid '}
                                          borderColor={'gray.50'}
                                          _hover={{
                                            bg: 'gray.50',
                                          }}
                                          bg={
                                            item?.slp_notes?.invoice_id
                                              ? 'green.50'
                                              : item?.slp_notes?.id
                                                ? 'yellow.50'
                                                : ''
                                          }
                                          color={
                                            item?.slp_notes?.invoice_id
                                              ? 'green.700'
                                              : item?.slp_notes?.id
                                                ? 'yellow.700'
                                                : 'primary.300'
                                          }
                                          maxW={'7.5rem'}
                                          cursor={'pointer'}
                                        >
                                          {item?.slp_notes?.invoice_id
                                            ? 'Invoice Created'
                                            : item?.slp_notes?.id
                                              ? 'Invoice Pending'
                                              : 'Create Invoice'}
                                        </Center>
                                      </Link>
                                    </Flex>
                                  ) : (
                                    <Link
                                      href={`/slp/${slp.id}/create-invoice/${item.id}`}
                                    >
                                      <Text
                                        fontSize={'sm'}
                                        border={'1px solid black'}
                                        px={'2'}
                                        rounded={'12px'}
                                        bg={
                                          item?.calendly_event_type ===
                                            'invitee.created' ||
                                          item?.calendly_event_type === null
                                            ? Colors.GREEN.SECONDARY
                                            : 'red'
                                        }
                                        color={'white'}
                                      >
                                        {item?.calendly_event_type ===
                                          'invitee.created' ||
                                        item?.calendly_event_type === null
                                          ? 'Confirmed'
                                          : 'Cancelled'}
                                      </Text>
                                    </Link>
                                  )}
                                </Flex>
                              </Flex>
                            )}
                          </Flex>
                          <Flex>
                            {item?.organization !== 1 && (
                              <PackageBalanceComp data={item} />
                            )}

                            {item?.organization_id === 1 && (
                              <SpeakFluentPackageComp data={item} />
                            )}
                          </Flex>
                        </Stack>
                      </Stack>
                    </Box>
                  ))
                )}
                {/* /slp/${slp.id}/create-invoice/${item.id} */}

                {/* <Button
                  variant={'outline'}
                  onClick={() => handleTabChange('calendar')}
                  rounded={'8px'}
                >
                  <LuCalendar />
                  View Full Calendar
                </Button> */}
              </Stack>
            )}
          </Stack>
        </GridItem>
        <GridItem colSpan={2}>
          <Stack
            w={'full'}
            h={'full'}
            border={'1px solid'}
            borderColor={'gray.50'}
            rounded={'12px'}
            px={{ base: '4', lg: '6' }}
            py={'3'}
          >
            <Heading
              fontSize={'1.2rem'}
              display={'flex'}
              alignItems={'center'}
              gap={'2'}
            >
              <FiCheckCircle color="green" />
              Your Tasks
            </Heading>
            <Text
              mt={'-1.5'}
              fontSize={'0.8rem'}
              fontWeight={'medium'}
              color={'gray.300'}
            >
              Upcoming follow-ups and action items
            </Text>

            <Stack pt={'4'} pb={'2'} gap={'4'}>
              {showTodo?.length === 0 ? (
                <Text textAlign={'center'} color={'gray.500'}>
                  No tasks available
                </Text>
              ) : (
                showTodo?.map((item: any) => (
                  <HStack
                    key={item.id}
                    border={'1px solid'}
                    borderColor={'gray.50'}
                    p={'2'}
                    px={'4'}
                    rounded={'8px'}
                    gap={'2'}
                    alignItems={'flex-start'}
                  >
                    <Box
                      w={'3'}
                      h={'3'}
                      bg={'green.500'}
                      rounded={'100px'}
                      mt={'2'}
                    />
                    <VStack
                      justifyContent={'space-between'}
                      w={'full'}
                      alignItems={'start'}
                    >
                      <Stack>
                        <Text fontWeight={'semibold'}>{item.task}</Text>
                        {/* {item.client_name && (
                          <Text fontSize={'sm'} mt={'-1'}>
                            Client: {item.client_name}
                          </Text>
                        )} */}
                        <Text
                          color={'gray.400'}
                          fontSize={'md'}
                          mt={'-2.5'}
                          textTransform={'capitalize'}
                        >
                          {/* Status:{' '}
                          {item.status === 'undone' ? 'Not Done' : 'Done'} */}

                          {item?.clients?.display_name}
                        </Text>
                      </Stack>
                      <Flex
                        justifyContent={'space-between'}
                        w={'full'}
                        alignItems={'center'}
                      >
                        <Text
                          fontSize={'sm'}
                          fontWeight={'500'}
                          color={'gray.200'}
                        >
                          {moment(item.created_at).calendar(null, {
                            sameDay: '[Today]',
                            lastDay: '[Yesterday]',
                            nextDay: '[Tomorrow]',
                            lastWeek: 'ddd MM/DD/YYYY',
                            sameElse: 'ddd MM/DD/YYYY',
                          })}
                        </Text>
                        <HStack
                          title="Mark as complete"
                          cursor="pointer"
                          color={'green.600'}
                          px={'1.5'}
                          fontWeight={'500'}
                          onClick={() => handleUpdateTodo(item)}
                          _hover={{
                            bg: 'green.50',
                          }}
                        >
                          <Icon>
                            <MdDone />
                          </Icon>
                          <Text>Mark Done</Text>

                          {/* <Icon
                            onClick={() => handleUpdateTodo(item)}
                            cursor={'pointer'}
                          >
                            <LuX />
                          </Icon> */}
                        </HStack>
                      </Flex>
                    </VStack>
                  </HStack>
                ))
              )}
              <Button
                variant={'outline'}
                onClick={todoHook.onOpen}
                rounded={'8px'}
                border={'1px solid'}
                borderColor={'gray.50'}
              >
                <LuPlus />
                Add New Task
              </Button>
            </Stack>
          </Stack>
        </GridItem>
      </SimpleGrid>

      <Stack
        w={'full'}
        minH={'32'}
        border={'1px solid'}
        borderColor={'gray.50'}
        rounded={'12px'}
        mt={'3'}
        px={{ base: '4', lg: '6' }}
        py={'3'}
      >
        <Flex
          justifyContent={{ md: 'space-between' }}
          gap={'3'}
          alignItems={{ md: 'center' }}
          flexDirection={{ base: 'column', md: 'row' }}
        >
          <VStack alignItems={'start'} gap={'0.5'} justifyContent={'start'}>
            <Heading
              fontSize={'1.2rem'}
              display={'flex'}
              alignItems={'center'}
              gap={'2'}
            >
              <Box color={'purple.500'}>
                <FaRegChartBar />
              </Box>
              Client Analytics
            </Heading>
            <Text fontSize={'0.8rem'} fontWeight={'medium'} color={'gray.300'}>
              Trends to optimize client engagement strategies
            </Text>
          </VStack>

          <Button
            variant={'outline'}
            onClick={() => handleTabChange('clients')}
            rounded={'8px'}
            borderColor={'gray.50'}
          >
            View Client Dashboard
          </Button>
        </Flex>

        <Stack pt={'4'} pb={'2'} gap={'4'}>
          <SimpleGrid columns={{ base: 2, md: 3, lg: 4 }} gap={'6'} pt={'2'}>
            {analyticsList?.map((item) => (
              <Stack
                key={item.id}
                w={'full'}
                h={'32'}
                px={{ base: '4', lg: '6' }}
                py={'3'}
                justifyContent={'center'}
                alignItems={{ base: 'center', md: 'start' }}
              >
                <HStack justifyContent={'space-between'}>
                  <Text fontSize={'0.9rem'} color={'gray.300'}>
                    {item.title}
                  </Text>
                </HStack>
                <Text
                  mt={1}
                  fontSize={'2rem'}
                  fontWeight={'bold'}
                  color={'gray.900'}
                >
                  {item.value}
                </Text>
                {/* <Text fontSize={'xs'}>{item.note}</Text> */}
              </Stack>
            ))}
          </SimpleGrid>
        </Stack>
      </Stack>

      <AddTask hook={todoHook} />
    </Stack>
  );
}
