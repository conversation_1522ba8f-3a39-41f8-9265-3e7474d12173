import { useState, useCallback, useEffect, FormEvent } from 'react';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { useUpdateClientMutation } from '@/api/clients/update-client';
import { useQueryClient } from '@tanstack/react-query';
import { queryKey } from '@/constants/query-key';
import { toaster } from '@/components/ui/toaster';
import { useCountryStateCityHook } from '@/hooks/countryStateCity/useCountryStateCityHook';

interface UseEditClientHookProps {
  onClose: () => void;
  data: any; // Client data to edit
}

export const useEditClientHook = ({
  onClose,
  data,
}: UseEditClientHookProps) => {
  const [loading, setLoading] = useState(false);
  const [displayNameError, setDisplayNameError] = useState(false);
  const [error, setError] = useState(false);
  const [isUserEditingDisplayName, setIsUserEditingDisplayName] =
    useState('rendered');
  const queryClient = useQueryClient();

  const updateClientMutation = useUpdateClientMutation({
    onSuccess: () => {
      toaster.create({
        type: 'success',
        description: 'Client updated successfully',
      });
      // Invalidate the SLP client tables to refresh the data
      queryClient.invalidateQueries([queryKey.users.getSlpIdView]);
      queryClient.invalidateQueries([queryKey.users.getNonInvoicedClients]);
      queryClient.invalidateQueries([queryKey.client.search]);
      queryClient.invalidateQueries([queryKey.client.searchAll]);
      onClose();
      setLoading(false);
    },
    onError: (error: any) => {
      toaster.create({
        type: 'error',
        description: error?.message || 'Failed to update client',
      });
      setLoading(false);
    },
  });

  // Initialize form values with existing client data
  const initialValues = {
    first_name: data?.first_name || '',
    last_name: data?.last_name || '',
    middle_name: data?.middle_name || '',
    display_name: data?.display_name || '',
    phone: data?.phone || '',
    email: data?.email || '',
    province: data?.province || '',
    stage: data?.stage || '',
    country: data?.country || null,
    state: data?.state || null,
    city: data?.city || null,
  };

  const validationSchema = Yup.object({
    first_name: Yup.string().trim().required('First name is required'),
    last_name: Yup.string().trim().required('Last name is required'),
    email: Yup.string()
      .email('Invalid email address')
      .required('Email is required'),
    stage: Yup.string().required('Stage is required'),
  });

  const validateSelection = useCallback(
    (value: string) => {
      const [firstName, lastName] = value.trim().split(' ');
      if (firstName && lastName) setError(false);
      return !(firstName && lastName);
    },
    [setError]
  );

  const {
    values,
    handleSubmit,
    errors,
    touched,
    handleChange,
    setFieldValue,
    resetForm,
  } = useFormik({
    initialValues: initialValues,
    validationSchema,
    enableReinitialize: true, // This allows the form to reinitialize when data changes
    onSubmit: async (values) => {
      try {
        setLoading(true);

        if (displayNameError) {
          setError(true);
          return;
        }

        const updatePayload = {
          first_name: values.first_name,
          last_name: values.last_name,
          middle_name: values.middle_name,
          phone: values.phone || null,
          email: values.email?.toLowerCase() || null,
          display_name: values.display_name,
          province: values.province,
          stage: values.stage,
          country: values?.country,
          state: values?.state,
          city: values?.city,
        };

        await updateClientMutation.mutateAsync({
          id: data.id,
          data: updatePayload,
        });
      } catch (error) {
        console.error('Update client error:', error);
      }
    },
  });

  const { countryOptions, stateOptions, cityOptions } = useCountryStateCityHook(
    {
      countryCode: values?.country?.isoCode,
      stateCode: values?.state?.isoCode,
    }
  );

  useEffect(() => {
    if (isUserEditingDisplayName === 'false') {
      const firstName = values.first_name?.trim() || '';
      const lastName = values.last_name?.trim() || '';
      const autoGeneratedDisplayName = `${firstName} ${lastName}`.trim();

      if (autoGeneratedDisplayName) {
        setFieldValue('display_name', autoGeneratedDisplayName);
      } else {
        setFieldValue('display_name', '');
      }
      setDisplayNameError(validateSelection(autoGeneratedDisplayName));
    }
  }, [
    values.first_name,
    values.last_name,
    isUserEditingDisplayName,
    setDisplayNameError,
    validateSelection,
    setFieldValue,
  ]);

  const handleDisplayNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setIsUserEditingDisplayName('true');
    setFieldValue('display_name', value);
    setDisplayNameError(validateSelection(value));
  };

  const handleFirstNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setIsUserEditingDisplayName('false');
    setFieldValue('first_name', value);
  };

  const handleLastNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setIsUserEditingDisplayName('false');
    setFieldValue('last_name', value);
  };

  const handleFormSubmit = (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    handleSubmit();
  };

  return {
    values,
    touched,
    handleFormSubmit,
    errors,
    handleChange,
    setFieldValue,
    handleFirstNameChange,
    handleLastNameChange,
    handleDisplayNameChange,
    loading,
    stateOptions,
    countryOptions,
    cityOptions,
    resetForm,
    displayNameError,
    error,
  };
};
