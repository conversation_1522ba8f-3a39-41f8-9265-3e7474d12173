import EmptyState from '@/components/elements/EmptyState';
import { Box, Center, Flex, Text } from '@chakra-ui/react';
import moment from 'moment';
import { useState } from 'react';
import { FaCheck } from 'react-icons/fa';
import Answers from './Answers';

interface AnswerDetail {
  ans?: string | any[] | null;
  qt?: string;
  id?: string;
  page?: number;
  show_answers?: string;
}

interface FormAnswer {
  id: string;
  form_title?: string;
  created_at: string;
  answer_details: AnswerDetail[];
}

interface AllFormsProps {
  data: {
    id: string;
    first_name: string;
    last_name: string;
    form_title?: string;
    form_answers?: FormAnswer[];
  };
}

const AllForms = ({ data }: AllFormsProps) => {
  const [selectedForm, setSelectedForm] = useState<FormAnswer | null>(null);

  if (!data || !data?.form_answers || data?.form_answers.length === 0) {
    return <EmptyState text="No form answers found" />;
  }

  if (selectedForm) {
    return (
      <Answers answer={selectedForm} onBack={() => setSelectedForm(null)} />
    );
  }

  return (
    <Box width="100%">
      {data.form_answers
        .sort(
          (a: any, b: any) =>
            moment(b.created_at).valueOf() - moment(a.created_at).valueOf()
        )
        .map((formAnswer) => {
          const answer_details = formAnswer.answer_details || [];
          const numOfQuestions = answer_details.length;
          const numOfAnswered = answer_details.filter(
            (item) =>
              item.ans !== undefined && item.ans !== null && item.ans !== ''
          ).length;

          return (
            <Box
              key={formAnswer.id}
              borderRadius="md"
              width={'100%'}
              p={4}
              bg="white"
              cursor="pointer"
              _hover={{ backgroundColor: 'orange.100' }}
              boxShadow="md"
              transition="all 0.2s ease-in-out"
              mb={4}
              onClick={() => setSelectedForm(formAnswer)}
            >
              <Flex justify="space-between" align="center">
                <Flex align="center" gap={3}>
                  <Center
                    h={'40px'}
                    w={'40px'}
                    bg={'orange.500'}
                    rounded={'full'}
                  >
                    <FaCheck size={15} color="white" />
                  </Center>
                  <Flex direction="column">
                    <Text fontWeight="500">
                      {formAnswer?.form_title || 'Untitled Form'}
                    </Text>
                    <Text fontSize="sm" color="gray.500">
                      {moment(formAnswer?.created_at).format(
                        'MMM D, YYYY h:mma'
                      )}
                    </Text>
                  </Flex>
                </Flex>

                <Box textAlign="right" fontSize="12px">
                  <Text fontWeight="500">
                    {numOfQuestions} question{numOfQuestions !== 1 ? 's' : ''}
                  </Text>
                  <Text color="gray.500">
                    {numOfAnswered}/{numOfQuestions} completed
                  </Text>
                </Box>
              </Flex>
            </Box>
          );
        })}
    </Box>
  );
};

export default AllForms;
