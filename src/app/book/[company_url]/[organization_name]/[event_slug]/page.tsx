export const fetchCache = 'force-no-store';

import { getEventsBySlugAndCreator, getUserBuSlug } from '@/app/service/events';
import { env } from '@/constants/env';
import { Box, Flex, Separator } from '@chakra-ui/react';
import { createClient } from '@supabase/supabase-js';
import React from 'react';
import EventDetails from './_components/EventDetails';
import { notFound } from 'next/navigation';
import BookingForm from './_components/BookingForm';
import StripeProvider from './_components/StripeProvider';

const supabase = createClient(
  env.SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function generateMetadata({ params }: any) {
  const userDetails = await getUserBuSlug(supabase, params.organization_name);

  const eventDetails = await getEventsBySlugAndCreator(
    supabase,
    params.event_slug,
    params.organization_name
  );
  if (!userDetails) {
    return {
      title: 'User Not Found',
    };
  }
  if (!eventDetails) {
    return {
      title: 'Event Not Found',
    };
  }

  return {
    title: `Book ${eventDetails.title} with ${userDetails?.event_slug} | Speakfluent`,
    description: `Schedule a ${eventDetails.duration}-minute ${eventDetails.title} event with ${userDetails?.event_slug}.`,
  };
}
export default async function page({ params }: any) {
  const eventDetails = await getEventsBySlugAndCreator(
    supabase,
    params.event_slug,
    params.organization_name
  );

  const userDetails = await getUserBuSlug(supabase, params.organization_name);

  // const availability = await generateAvailability(supabase, params.event_slug);

  // console.log('organization is ', organizationDetails);

  if (!eventDetails) {
    notFound();
  }
  return (
    <div>
      <Box minH={'100vh'} pt={'2rem'}>
        <Box
          rounded={'.5rem'}
          boxShadow={'lg'}
          maxW={'65rem'}
          mx={'auto'}
          h={'85vh'}
          bg={'white'}
        >
          <Flex h={'100%'} pl={'3rem'} pr={'1rem'} gap={'3rem'}>
            <Box w={'20rem'}>
              <EventDetails
                organizationDetails={userDetails?.organization}
                eventData={eventDetails}
              />
            </Box>
            <Separator
              borderColor={'rgba(0,0,0,0.15)'}
              orientation={'vertical'}
              h={'100%'}
            />
            <Box flex={1}>
              <StripeProvider userDetails={userDetails}>
                <BookingForm
                  userDetails={userDetails}
                  eventData={eventDetails}
                  organizationDetails={userDetails?.organization}
                />
              </StripeProvider>
            </Box>
          </Flex>
        </Box>
      </Box>
    </div>
  );
}
