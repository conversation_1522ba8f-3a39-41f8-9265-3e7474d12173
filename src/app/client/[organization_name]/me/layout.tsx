'use client';

import { Box, Separator, Stack } from '@chakra-ui/react';
import { Sidebar } from './Sidebar';
import NavBar from './NavBar';

export default function layout({ children, params }: any) {
  console.log('in layout ', params);

  return (
    <Box
      bg={'white'}
      maxW={'100vw'}
      display={{ base: 'block', lg: 'flex' }}
      overflow={{ lg: 'hidden' }}
      p={'0'}
      w={'full'}
      h={'100vh'}
    >
      <Sidebar
        maxW="13.5rem"
        position="sticky"
        top="0"
        height="100vh"
        hideBelow="lg"
        organization_name={params.organization_name}
      />
      <Stack p={'0'} gap={0} flex="1" maxW={'full'} overflowX={'hidden'}>
        <Box bgColor={'white'}>
          <NavBar organization_name={params.organization_name} />
        </Box>
        <Separator mb={'1rem'} borderColor={'gray.50'} />

        <Box
          pt={'1rem'}
          pb={'10'}
          px={{ base: '6', lg: '8' }}
          overflow={'auto'}
          className="no-scrollbar"
        >
          {children}
        </Box>
      </Stack>
    </Box>
  );
}
