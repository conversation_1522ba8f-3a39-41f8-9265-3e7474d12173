import { buildUrlWithQueryParams } from '@/utils/build-url-query';

export async function getInvoiceById(id: any) {
  const response = await fetch(`/api/newsf/invoice/${id}`, {
    method: 'GET',
  });
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Failed to fetch invoice');
  }
  const data = await response.json();
  return data;
}
export async function updateInvoiceById(data: { id: any; payload: any }) {
  const response = await fetch(`/api/newsf/invoice/${data.id}`, {
    method: 'PUT',
    body: JSON.stringify(data.payload),
  });
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Failed to update invoice');
  }
  return await response.json();
}
export async function getAllInvoices(filter: any) {
  const baseUrl = '/api/newsf/invoice';
  const apiUrl = buildUrlWithQueryParams(baseUrl, filter);
  const response = await fetch(apiUrl, {
    method: 'GET',
  });
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Failed to fetch invoices');
  }
  const data = await response.json();
  return data;
}

export const createInvoice = async (body: any) => {
  const response = await fetch(`/api/newsf/invoice`, {
    method: 'POST',
    body: JSON.stringify(body),
  });
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Error creating invoice');
  }
  const data = await response.json();
  return data;
};

export const linkInvoiceWithTransaction = async (body: any) => {
  const response = await fetch(`/api/newsf/invoice/link-transaction`, {
    method: 'POST',
    body: JSON.stringify(body),
  });
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(
      errorData.message || 'Error linking invoice with transaction'
    );
  }
  const data = await response.json();
  return data;
};

// ================ TASK SERVICES =======================

export async function getTaxById(id: any) {
  const response = await fetch(`/api/newsf/taxes/${id}`, {
    method: 'GET',
  });
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Failed to fetch tax');
  }
  const data = await response.json();
  return data;
}
export async function getAllTaxes(filter: any) {
  const baseUrl = '/api/newsf/taxes';
  const apiUrl = buildUrlWithQueryParams(baseUrl, filter);
  const response = await fetch(apiUrl, {
    method: 'GET',
  });
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Failed to fetch taxes');
  }
  const data = await response.json();
  return data;
}

export const createTax = async (body: any) => {
  const response = await fetch(`/api/newsf/taxes`, {
    method: 'POST',
    body: JSON.stringify(body),
  });
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Error creating tax');
  }
  const data = await response.json();
  return data;
};

export const updateTaxById = async (body: any) => {
  const response = await fetch(
    `/api/newsf/taxes/${body.id}?org_id=${body.org_id}`,
    {
      method: 'PUT',
      body: JSON.stringify(body.payload),
    }
  );
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Error updating tax');
  }
  const data = await response.json();
  return data;
};
export const deleteTaxById = async (payload: any) => {
  const response = await fetch(
    `/api/newsf/taxes/${payload?.id}?org_id=${payload.org_id}`,
    {
      method: 'DELETE',
    }
  );
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Error deleting tax');
  }
  const data = await response.json();
  return data;
};

// =================== PURCHASES ========================
export async function getAllPurchases(filter: any) {
  const baseUrl = '/api/newsf/purchases';
  const apiUrl = buildUrlWithQueryParams(baseUrl, filter);
  const response = await fetch(apiUrl, {
    method: 'GET',
  });
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Failed to fetch purchases');
  }
  const data = await response.json();
  return data;
}

export async function updatePurchaseById(data: { id: any; payload: any }) {
  const response = await fetch(`/api/newsf/purchases/${data.id}`, {
    method: 'PUT',
    body: JSON.stringify(data.payload),
  });
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Failed to update purchases');
  }
  return await response.json();
}
