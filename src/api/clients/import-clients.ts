//OPTIMIZED
import { MutationConfig, useMutation } from '@/lib/react-query';
import { ToastMessages } from '@/constants/toast-messages';
import { queryKey } from '@/constants/query-key';
import { toaster } from '@/components/ui/toaster';

const importClients = async (body: any) => {
  const response = await fetch(`/api/clients/import`, {
    method: 'POST',
    body: JSON.stringify(body),
  });

  console.log('response in import client is ', response);
  const data = await response.json();
  if (!response.ok) throw new Error(data?.message);

  return data;
};

type QueryFnType = typeof importClients;

export const useImportClientsMutation = (
  config?: MutationConfig<QueryFnType>
) => {
  return useMutation({
    onError: (err: any) => {
      console.log('err in client is ', err);

      toaster.create({
        type: 'error',
        description: err?.message || ToastMessages.somethingWrong,
      });
    },

    retry: false,
    mutationKey: [queryKey.client.import],
    mutationFn: importClients,
    ...config,
  });
};
