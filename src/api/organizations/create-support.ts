import { MutationConfig, useMutation } from '@/lib/react-query';
import { ToastMessages } from '@/constants/toast-messages';
import { toaster } from '@/components/ui/toaster';

const createSupoort = async (body: FormData) => {
  const response = await fetch(`/api/support`, {
    method: 'POST',
    body: body,
  });
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Error updating slp note');
  }
  return response.json();
};

type QueryFnType = typeof createSupoort;

export const useCreateSupoortApi = (config?: MutationConfig<QueryFnType>) => {
  return useMutation({
    onError: (err: any) => {
      toaster.create({
        type: 'error',
        description: ToastMessages.somethingWrong || err?.message,
      });
    },
    onSuccess: () => {
      toaster.create({
        type: 'success',
        description: 'Submitted successfully',
      });
    },
    retry: false,
    mutationKey: ['create support'],
    mutationFn: createSupoort,
    ...config,
  });
};
