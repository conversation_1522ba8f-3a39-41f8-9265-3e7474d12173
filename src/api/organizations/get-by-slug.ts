import { query<PERSON><PERSON> } from '@/constants/query-key';
import {
  ExtractFnReturnType,
  QueryConfigType,
  useQuery,
} from '@/lib/react-query';
import { TIsPublic } from '@/shared/interface';

export async function getOrganizationBySlug(
  slug: string,
  isPublic: TIsPublic = 'false'
) {
  const response = await fetch(`/api/organizations/slug/${slug}`, {
    method: 'GET',
    cache: 'no-store',
    headers: {
      'x-public-access': isPublic,
    },
  });

  const json = await response.json();
  return json;
}

type QueryFnType = typeof getOrganizationBySlug;

export const useGetOrganizationBySlugQuery = (
  data: {
    slug: string;
    isPublic?: TIsPublic;
  },
  config?: QueryConfigType<QueryFnType>
) => {
  const { slug, isPublic } = data;
  return useQuery<ExtractFnReturnType<QueryFnType>>({
    retry(failureCount, error: any) {
      if ([404, 401].includes(error.status)) return false;
      else if (failureCount < 1) return true;
      else return false;
    },
    queryKey: [queryKey.organizations.getBySlug, slug, isPublic],
    queryFn: () => getOrganizationBySlug(slug, isPublic),
    ...config,
  });
};
