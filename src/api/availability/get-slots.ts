//OPTIMIZED
import { queryKey } from '@/constants/query-key';
import {
  ExtractFnReturnType,
  QueryConfigType,
  useQuery,
} from '@/lib/react-query';

export async function getSlots({
  slug,
  selectedMonth,
  timezone,
  orgData,
}: any) {
  const response = await fetch('/api/availability/slots', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      slug,
      date: new Date(selectedMonth),
      timezone,
      orgData,
    }),
  });
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Failed to fetch slots');
  }
  const data = await response.json();
  console.log('data in query is ', data);

  return data;
}

type QueryFnType = typeof getSlots;

type options = QueryConfigType<QueryFnType>;
export const useGetSlotsQuery = (data: any, config?: options) => {
  const { orgData, ...res } = data;
  return useQuery<ExtractFnReturnType<QueryFnType>>({
    retry(failureCount, error: any) {
      if ([404, 401].includes(error.status)) return false;
      else if (failureCount < 1) return true;
      else return false;
    },
    queryKey: [queryKey.bookings.getSlots, res],
    queryFn: () => getSlots({ orgData, ...res }),
    ...config,
  });
};
