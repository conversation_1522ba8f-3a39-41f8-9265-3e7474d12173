import { QueryConfigType, useQuery } from '@/lib/react-query';
import supabase from '@/lib/supabase/client';
import 'moment-timezone';
import 'moment/locale/en-ca';
import { tableNames } from '@/constants/table_names';
import { subDays, formatISO } from 'date-fns';
import { useRecoilValue } from 'recoil';
import {
  IOverviewFilterState,
  OverviewFilterState,
} from '@/store/filters/overview';

export async function getOverview(filter: IOverviewFilterState) {
  const startDate = subDays(new Date(), filter.selectedDate);
  const endDate = new Date();
  const start = formatISO(startDate);
  const end = formatISO(endDate);

  // Fetching data with await
  const { data: bookings, error: bookingsError } = await supabase
    .from(tableNames.bookings)
    .select(`*`)
    .gte('created_at', start)
    .lte('created_at', end);

  const { data: clients, error: clientsError } = await supabase
    .from(tableNames.clients)
    .select(`*`);
  // .gte('created_at', start)
  // .lte('created_at', end);

  const { data: users, error: usersError } = await supabase
    .from(tableNames.users)
    .select(`*`)
    .gte('created_at', start)
    .lte('created_at', end);

  // Handling errors (optional)
  if (bookingsError || clientsError || usersError) {
    throw new Error('Error fetching data');
  }

  return {
    data: { bookings, clients, users },
    error: bookingsError || clientsError || usersError,
  } as any;
}

type QueryFnType = typeof getOverview;

export const useGetOverviewApi = (config?: QueryConfigType<QueryFnType>) => {
  const filter = useRecoilValue(OverviewFilterState);

  return useQuery({
    retry(failureCount: any, error: any) {
      if ([404, 401].includes(error.status)) return false;
      else if (failureCount < 1) return true;
      else return false;
    },
    queryKey: ['get-all-overview', filter],
    queryFn: () => getOverview(filter as IOverviewFilterState),
    ...config,
  });
};
