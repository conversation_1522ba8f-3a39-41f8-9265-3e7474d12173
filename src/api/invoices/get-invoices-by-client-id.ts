//OPTIMIZED
import { queryKey } from '@/constants/query-key';
import {
  ExtractFnReturnType,
  QueryConfigType,
  useQuery,
} from '@/lib/react-query';
import { TIsPublic } from '@/shared/interface';

export async function getInvoicesByClient(
  id: number,
  isPublic: TIsPublic = 'false'
) {
  const response = await fetch(`/api/invoices/client/${id}`, {
    method: 'GET',
    headers: {
      'x-public-access': isPublic,
    },
  });
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Unable to fetch invoices');
  }
  const data = await response.json();
  return data;
}
type QueryFnType = typeof getInvoicesByClient;

export const useGetInvoicesByClientQuery = (
  data: { id: number; isPublic?: TIsPublic },
  config?: QueryConfigType<QueryFnType>
) => {
  return useQuery<ExtractFnReturnType<QueryFnType>>({
    retry(failureCount, error: any) {
      if ([404, 401].includes(error.status)) return false;
      else if (failureCount < 1) return true;
      else return false;
    },
    queryKey: [queryKey.invoices.getByClients, data],
    queryFn: () => getInvoicesByClient(data.id, data.isPublic),
    ...config,
  });
};
