import { useQuery } from '@/lib/react-query';
import { queryKey } from '@/constants/query-key';
import { IUser } from '@/shared/interface/user';

type PendingInvoicesStatsResponse = {
  pending_count: number;
  pending_total_amount: number;
  revenue_count: number;
  revenue_total_amount: number;
};

async function fetchPendingInvoicesStats({
  slp,
}: {
  slp: IUser;
}): Promise<PendingInvoicesStatsResponse> {
  const organizationId = slp?.organization_id;
  const slpId = slp?.id;

  if (!organizationId || !slpId) {
    throw new Error('Missing organization_id or slp_id');
  }

  const url = new URL('/api/invoices/not-paid', window.location.origin);
  url.searchParams.set('organization_id', organizationId.toString());
  url.searchParams.set('slp_id', slpId.toString());

  const res = await fetch(url.toString(), { method: 'GET' });
  const json = await res.json();

  if (!res.ok) {
    throw new Error(json.message || 'Failed to fetch pending invoices stats');
  }

  return json;
}

export const usePendingInvoicesStatsQuery = (slp: IUser | undefined) => {
  return useQuery<PendingInvoicesStatsResponse>({
    queryKey: [queryKey.invoices.getNotPaid, slp?.id],
    queryFn: () => fetchPendingInvoicesStats({ slp: slp! }),
    enabled: !!slp?.id,
  });
};
