import { tableNames } from '@/constants/table_names';
import { SupabaseClient } from '@supabase/supabase-js';
import { KlaviyoAPI, KlaviyoEvent } from './api';
import { KlaviyoActions } from '@/constants/klaviyo-actions';

export const getKlaviyoOrganization = async (
  organization_id: string,
  supabase: SupabaseClient
) => {
  const { data: KlaviyoDetails, error: DetailsError } = await supabase
    .from(tableNames.klaviyo_organization)
    .select('*')
    .eq('organization_id', organization_id)
    .maybeSingle();
  if (DetailsError) throw DetailsError;
  return KlaviyoDetails;
};
export const dispatchBookingCreatedEvent = async (
  organization_id: string,
  supabase: SupabaseClient,
  event: any
) => {
  try {
    const KlaviyoDetails = await getKlaviyoOrganization(
      organization_id,
      supabase
    );

    const klaviyoApi = new KlaviyoAPI(
      KlaviyoDetails?.access_token,
      KlaviyoDetails?.refresh_token
    );
    await klaviyoApi.trackEvent(
      organization_id,
      KlaviyoActions.BOOKING_CREATED,
      event,
      supabase
    );
  } catch (klaviyoError: any) {
    console.warn('Failed to track Klaviyo event in route:', klaviyoError);
  }
};
export const dispatchKlaviyoEvent = async (
  organization_id: string,
  supabase: SupabaseClient,
  event: KlaviyoEvent,
  eventType: string
) => {
  try {
    const KlaviyoDetails = await getKlaviyoOrganization(
      organization_id,
      supabase
    );
    const klaviyoApi = new KlaviyoAPI(
      KlaviyoDetails?.access_token,
      KlaviyoDetails?.refresh_token
    );
    await klaviyoApi.trackEvent(organization_id, eventType, event, supabase);
  } catch (klaviyoError: any) {
    console.warn('Failed to track Klaviyo event in route:', klaviyoError);
  }
};
