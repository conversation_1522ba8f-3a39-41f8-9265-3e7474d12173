import { SupabaseClient } from '@supabase/supabase-js';
import { KlaviyoTokens } from './oauth';
import { tableNames } from '@/constants/table_names';
// import supabase from '../supabase/client';

export class KlaviyoDatabase {
  /**
   * Save Klaviyo integration for a Organization
   */
  static async saveIntegration(
    organization_id: string,
    tokens: KlaviyoTokens,
    accountInfo: any,
    supabase: SupabaseClient
  ): Promise<any> {
    //Verify Organization
    const { data: OrganizationDetails, error: OrganizationError } =
      await supabase
        .from(tableNames.organizations)
        .select('id')
        .eq('id', Number(organization_id))
        .maybeSingle();
    if (OrganizationError) throw OrganizationError;
    if (!OrganizationDetails) throw new Error('Organization not found');

    console.log('tokens is ', tokens);

    //Organization Exist
    const expiresAt = new Date();
    expiresAt.setSeconds(expiresAt.getSeconds() + tokens.expires_in);
    await supabase.from(tableNames.klaviyo_organization).insert({
      organization_id: Number(organization_id),
      access_token: tokens.access_token,
      refresh_token: tokens.refresh_token,
      account_name:
        accountInfo.attributes?.contact_information?.organization_name,
      account_id: accountInfo.id,
      expires_at: expiresAt,
    });
  }

  /**
   * Get Klaviyo integration for a Organization
   */
  static async getIntegration(
    organization_id: string,
    supabase: SupabaseClient
  ): Promise<any> {
    return supabase
      .from(tableNames.klaviyo_organization)
      .select('*')
      .eq('organization_id', Number(organization_id))
      .maybeSingle();
  }

  /**
   * Update tokens after refresh
   */
  static async updateTokens(
    organization_id: string,
    tokens: KlaviyoTokens,
    supabase: SupabaseClient
  ): Promise<any> {
    const expiresAt = new Date();
    expiresAt.setSeconds(expiresAt.getSeconds() + tokens.expires_in);

    return await supabase
      .from(tableNames.klaviyo_organization)
      .update({
        access_token: tokens.access_token,
        refresh_token: tokens.refresh_token,
        updatedAt: new Date(),
        expires_at: expiresAt,
      })
      .select('*');
  }

  /**
   * Delete integration (when Organization disconnects)
   */
  static async deleteIntegration(
    organization_id: string,
    supabase: SupabaseClient
  ): Promise<void> {
    await supabase
      .from(tableNames.klaviyo_organization)
      .delete()
      .eq('organization_id', Number(organization_id));
  }
}
