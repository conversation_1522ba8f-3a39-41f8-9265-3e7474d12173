import { SupabaseClient } from '@supabase/supabase-js';
import { KLAVIYO_CONFIG } from './config';
import { KlaviyoOAuth, KlaviyoTokens } from './oauth';
import { tableNames } from '@/constants/table_names';

export interface KlaviyoProfile {
  id?: string;
  email?: string;
  phone_number?: string;
  first_name?: string;
  last_name?: string;
  organization?: string;
  location?: {
    address1?: string;
    address2?: string;
    city?: string;
    country?: string;
    region?: string;
    zip?: string;
  };
  properties?: Record<string, any>;
}

export interface KlaviyoEvent {
  data: {
    type: 'event';
    attributes: {
      profile: {
        data: {
          type: 'profile';
          attributes: {
            email: string;
            first_name?: string;
            last_name?: string;
            id?: string;
            [key: string]: any; // Allow additional custom properties
          };
        };
      };
      metric: {
        data: {
          type: 'metric';
          attributes: {
            name: string;
          };
        };
      };
      properties: {
        [key: string]: any; // Allow additional custom properties
      };
      time?: string; // ISO 8601 format (e.g., "2025-07-29T07:58:00Z")
      value?: number; // Optional monetary value
      value_currency?: string; // Required if value is provided (e.g., "USD")
    };
  };
}

export class KlaviyoAPI {
  private accessToken: string;
  private refreshToken?: string;
  private onTokenRefresh?: (tokens: KlaviyoTokens) => Promise<void>;

  constructor(
    accessToken: string,
    refreshToken?: string,
    onTokenRefresh?: (tokens: KlaviyoTokens) => Promise<void>
  ) {
    this.accessToken = accessToken;
    this.refreshToken = refreshToken;
    this.onTokenRefresh = onTokenRefresh;
  }

  /**
   * Make authenticated API request with automatic token refresh
   */
  private async makeRequest(
    endpoint: string,
    options: RequestInit = {},
    supabase?: SupabaseClient,
    organization_id?: string
  ): Promise<Response> {
    const url = `${KLAVIYO_CONFIG.apiUrl}${endpoint}`;

    console.log('options is ', options);

    const response = await fetch(url, {
      ...options,
      headers: {
        Authorization: `Bearer ${this.accessToken}`,
        'Content-Type': 'application/json',
        Accept: 'application/json',
        revision: '2024-10-15', // Use latest API version
        ...options.headers,
      },
    });

    if (response.status === 401 && this.refreshToken) {
      console.log('trying to refresh');

      try {
        const newTokens = await KlaviyoOAuth.refreshAccessToken(
          this.refreshToken
        );
        console.log('refresh successful');

        this.accessToken = newTokens.access_token;
        this.refreshToken = newTokens.refresh_token;

        // Save new tokens
        if (this.onTokenRefresh) {
          await this.onTokenRefresh(newTokens);
        } else {
          if (supabase) {
            await supabase
              .from(tableNames.klaviyo_organization)
              .update({
                access_token: newTokens.access_token,
                refresh_token: newTokens.refresh_token,
                updated_at: new Date(),
              })
              .eq('organization_id', organization_id);
          }
        }

        console.log('trying to make request again successful');

        // Retry the request with new token
        return fetch(url, {
          ...options,
          headers: {
            Authorization: `Bearer ${this.accessToken}`,
            'Content-Type': 'application/json',
            Accept: 'application/json',
            revision: '2024-10-15',
            ...options.headers,
          },
        });
      } catch (error: any) {
        throw new Error('error is', error);
      }
    }

    return response;
  }

  /**
   * Get account information
   */
  async getAccount(): Promise<any> {
    const response = await this.makeRequest('/accounts/');
    if (!response.ok) {
      throw new Error(`Failed to get account: ${response.statusText}`);
    }
    const data = await response.json();
    return data.data[0]; // First account
  }

  /**
   * Create or update a profile
   */
  async createProfile(profile: KlaviyoProfile): Promise<any> {
    const response = await this.makeRequest('/profiles/', {
      method: 'POST',
      body: JSON.stringify({
        data: {
          type: 'profile',
          attributes: profile,
        },
      }),
    });

    if (!response.ok) {
      throw new Error(`Failed to create profile: ${response.statusText}`);
    }

    return response.json();
  }

  /**
   * Track an event
   */
  async trackEvent(
    organizationId: string,
    eventName: string,
    event: KlaviyoEvent,
    supabase: SupabaseClient
  ): Promise<any> {
    const isEnabled = await this.isEventEnabled(
      organizationId,
      eventName,
      supabase
    );
    if (!isEnabled) {
      console.log(
        `Event ${eventName} is not enabled for organization ${organizationId}`
      );
      return;
    }

    const response = await this.makeRequest(
      '/events/',
      {
        method: 'POST',
        body: JSON.stringify(event),
      }
      // organizationId
    );

    if (!response.ok) {
      throw new Error(
        `Failed to track event in make call: ${response.statusText}`
      );
    }

    return response;
  }

  /**
   * Get lists
   */
  async getLists(): Promise<any> {
    const response = await this.makeRequest('/lists/');
    if (!response.ok) {
      throw new Error(`Failed to get lists: ${response.statusText}`);
    }
    return response;
  }

  /**
   * Create a list
   */
  async createList(name: string): Promise<any> {
    const response = await this.makeRequest('/lists/', {
      method: 'POST',
      body: JSON.stringify({
        data: {
          type: 'list',
          attributes: {
            name,
          },
        },
      }),
    });

    if (!response.ok) {
      throw new Error(`Failed to create list: ${response.statusText}`);
    }

    return await response.json();
  }

  /**
   * Add profile to list
   */
  async addProfileToList(listId: string, profileId: string): Promise<any> {
    const response = await this.makeRequest(
      `/lists/${listId}/relationships/profiles/`,
      {
        method: 'POST',
        body: JSON.stringify({
          data: [
            {
              type: 'profile',
              id: profileId,
            },
          ],
        }),
      }
    );

    if (!response.ok) {
      throw new Error(`Failed to add profile to list: ${response.statusText}`);
    }

    return response.json();
  }

  /**
   * Get segments
   */
  async getSegments(): Promise<any> {
    const response = await this.makeRequest('/segments/');
    if (!response.ok) {
      throw new Error(`Failed to get segments: ${response.statusText}`);
    }
    return response.json();
  }

  /**
   * Get campaigns
   */
  async getCampaigns(): Promise<any> {
    const response = await this.makeRequest('/campaigns/');
    if (!response.ok) {
      throw new Error(`Failed to get campaigns: ${response.statusText}`);
    }
    return response.json();
  }

  /**
   * Get flows
   */
  async getFlows(): Promise<any> {
    const response = await this.makeRequest('/flows/');
    if (!response.ok) {
      throw new Error(`Failed to get flows: ${response.statusText}`);
    }
    return response;
  }

  // Check if an event is enabled for tracking
  async isEventEnabled(
    organizationId: string,
    eventName: string,
    supabase: SupabaseClient
  ): Promise<boolean> {
    const { data, error } = await supabase
      .from(tableNames.klaviyo_trackable_events)
      .select('is_enabled')
      .eq('organization_id', organizationId)
      .eq('event_name', eventName)
      .single();

    if (error || !data) {
      console.warn(
        `Event ${eventName} not found for organization ${organizationId}`
      );
      return false;
    }

    return data.is_enabled;
  }
}
