// components/invoice/InvoiceHeader.jsx
// import CustomDatePicker from '@/components/elements/date-picker/date-picker';
import SelectContact from '@/components/elements/search/SelectContact';
import StringInput from '@/components/Input/StringInput';
import { Colors } from '@/constants/colors';
import { Box, Flex, Stack, Text } from '@chakra-ui/react';
// import StringInput from '@/components/Input/StringInput';
import { useParams } from 'next/navigation';

export default function InvoiceHeader({
  client,
  invoiceDetails,
  handleInvoiceDetailsChange,
  isEditing,
  handleSelectClient,
}: any) {
  const params = useParams();
  const formatDate = (dateStr: any) => {
    // Handle ISO string format (e.g., "2024-11-13T23:00:00Z")
    const newDate = new Date(dateStr).toISOString();
    if (newDate && newDate?.includes('T')) {
      return newDate?.split('T')[0];
    }

    // Handle "YYYY-MM-DD HH:mm:ss" format
    if (newDate && newDate?.includes(' ')) {
      return newDate?.split(' ')[0];
    }

    // Return the original string if it doesn't match the expected formats
    return newDate;
  };
  const onContactReady = (options: any) => {
    const initialClient = options.find(
      (item: any) =>
        item.value.id === Number(params?.client_id || invoiceDetails?.client_id)
    );
    if (initialClient) {
      handleSelectClient(initialClient.value);
    }
  };

  return (
    <Flex
      direction={{ base: 'column', md: 'row' }}
      justifyContent={'space-between'}
      alignItems={'flex-start'}
      gap={6}
    >
      {/* Client Details */}
      <Box
        // display={'flex'}
        // p={4}
        // border="1px solid #EDF1F3"
        // borderRadius="md"
        alignItems="center"
        gap={4}
        minW={{ base: 'full', md: '250px' }}
        // cursor="pointer"
        // flexShrink={0}
      >
        <Box mb={'1rem'} w={'full'}>
          <SelectContact
            onReady={onContactReady}
            handleSelectClient={handleSelectClient}
          />
        </Box>
        <Box>
          <Text>Bill to</Text>
          <Text
            fontWeight={'bold'}
            color={Colors?.ORANGE?.PRIMARY}
            fontSize={'md'}
          >
            {client?.display_name}
          </Text>
          {client?.email || client?.email ? (
            <Text mt={4} fontWeight={'bold'} fontSize="sm" color="gray.600">
              {client?.email || client?.email}
            </Text>
          ) : null}
        </Box>
      </Box>

      {/* Invoice Details: Number, Date, Due Date, Memo */}
      <Stack gap={4}>
        {isEditing && (
          <Flex alignItems="center" gap={4}>
            <Text
              whiteSpace={'nowrap'}
              color={'gray.600'}
              fontWeight="medium"
              minW="8rem"
            >
              Invoice Number
            </Text>
            <StringInput
              inputProps={{
                placeholder: 'Start typing here...',
                value: invoiceDetails?.invoice_number,
                onChange: (e) =>
                  handleInvoiceDetailsChange('invoice_number', e.target.value),
              }}
            />
          </Flex>
        )}

        <Flex alignItems="center" gap={4}>
          <Text color={'gray.600'} fontWeight="medium" minW="8rem">
            Invoice Date
          </Text>

          <StringInput
            inputProps={{
              name: 'transaction_date',
              type: 'date',
              placeholder: 'Select Date',
              value: invoiceDetails.invoiceDate
                ? formatDate(invoiceDetails.invoiceDate)
                : '',
              onChange: (e) =>
                handleInvoiceDetailsChange('invoiceDate', e.target.value),
            }}
            fieldProps={
              {
                // label: 'Date',
                // required: true,
              }
            }
          />

          {/* <CustomDatePicker
            defaultDate={invoiceDetails.invoiceDate}
            onChange={(date) => handleInvoiceDetailsChange('invoiceDate', date)}
          /> */}
        </Flex>
        <Box>
          <Flex alignItems="center" gap={4}>
            <Text color={'gray.600'} fontWeight="medium" minW="8rem">
              Payment Due
            </Text>
            <Box w={'full'}>
              <StringInput
                inputProps={{
                  name: 'dueDate',
                  type: 'date',
                  placeholder: 'Select Date',
                  value: invoiceDetails.dueDate
                    ? formatDate(invoiceDetails.dueDate)
                    : '',
                  onChange: (e) =>
                    handleInvoiceDetailsChange('dueDate', e.target.value),
                }}
                fieldProps={
                  {
                    // label: 'Date',
                    // required: true,
                  }
                }
              />
              {/* <CustomDatePicker
              defaultDate={invoiceDetails.dueDate}
              onChange={(date) => handleInvoiceDetailsChange('dueDate', date)}
            /> */}
            </Box>
          </Flex>
          <Box display={'flex'} justifyContent={'flex-end'}>
            {' '}
            <Text color={'gray.500'} fontSize="xs" mt={1}>
              On Receipt
            </Text>
          </Box>
        </Box>
      </Stack>
    </Flex>
  );
}
