// components/invoice/InvoiceItemTable.jsx
import React from 'react';
import { Box, Flex, Text, Grid, Stack } from '@chakra-ui/react';
import { FiPlus, FiTrash2 } from 'react-icons/fi';
import { Colors } from '@/constants/colors';
import CustomSelect from '@/components/Input/CustomSelect';
import CustomTextArea from '@/components/Input/CustomTextArea';
import NumericInput from '@/components/Input/NumericInput';
import { formatMoney } from '@/components/elements/format-money/FormatMoney';

export default function InvoiceItemTable({
  selectedItems,
  // itemDisclosure,
  // invoiceItemOptions,
  // handleItemClick,
  updateItem,
  deleteItem,
  calculateItemTax,
  taxOptions,
  actions,
  invoiceDetails,
}: any) {
  // console.log('selectedItems is ', selectedItems);

  // const formatCurrency = (amount: number, currencyCode: string) => {
  //   return new Intl.NumberFormat('en-US', {
  //     style: 'currency',
  //     currency: currencyCode || 'USD',
  //   }).format(amount);
  // };

  return (
    <Box border="1px solid #EDF1F3" borderRadius="md">
      {/* Table Header */}
      <Grid
        // templateColumns={'4fr .5fr .5fr 0.5fr auto'}
        templateColumns={'3.5fr 1.5fr 1fr'} // Adjusted columns for better alignment
        bg={'#F8F9FA'} // Lighter background for header
        py={3}
        px={6}
        fontWeight={'semibold'}
        color="gray.700"
        borderBottom="1px solid #EDF1F3"
        gap={4}
      >
        <Box display={'grid'} gap={'.5rem'} gridTemplateColumns={'1fr 1fr'}>
          <Text>Item</Text>
          <Text display={{ base: 'none', md: 'block' }}>Description</Text>
        </Box>{' '}
        {/* & Description */}
        <Box display={'grid'} gap={'1rem'} gridTemplateColumns={'0.5fr 1fr'}>
          <Text flexGrow={1}>Quantity</Text>
          <Text flexGrow={1}>Price</Text>
        </Box>
        <Box>
          <Text textAlign="right">Amount</Text>
          <Text></Text> {/* For delete icon */}
        </Box>
      </Grid>

      {/* Add Item Select (conditionally rendered) */}

      {/* Actual Items */}
      <Stack gap={0}>
        {selectedItems?.map((selectedItem: any, index: any) => {
          const itemTaxAmount = calculateItemTax(selectedItem);
          const itemTotalAmount =
            (Number(selectedItem?.quantity) || 0) *
            (Number(selectedItem?.price) || 0);
          const isEven = index % 2 === 0;

          return (
            <Box key={selectedItem?.itemId} bg={!isEven ? 'gray.50' : 'white'}>
              <Grid
                templateColumns={{ base: '1fr', md: '3.5fr 1.5fr 1fr' }}
                px={6}
                pt={6}
                borderBottom="1px solid #EDF1F3"
                alignItems="start"
                gap={4}
              >
                <Box
                  display={'grid'}
                  gap={'.5rem'}
                  gridTemplateColumns={'1fr 1fr'}
                >
                  {/* Item Name and Description */}
                  {/* <Stack gap={2}> */}
                  <CustomTextArea
                    inputProps={{
                      value: selectedItem?.name || '',
                      onChange: (e) => {
                        updateItem(
                          'name',
                          e.target.value,
                          selectedItem?.itemId
                        );
                      },
                      placeholder: 'Enter item name',
                      size: 'sm',
                      // rows: 1,
                    }}
                  />
                  <Box>
                    <CustomTextArea
                      inputProps={{
                        position: 'absolute',
                        value: selectedItem?.description || '',
                        onChange: (e) => {
                          updateItem(
                            'description',
                            e.target.value,
                            selectedItem?.itemId
                          );
                        },
                        placeholder: 'Enter item description',
                        size: 'sm',
                        // rows: 1,
                      }}
                    />
                  </Box>
                </Box>

                <Box
                  display={'grid'}
                  gap={'1rem'}
                  alignItems={'center'}
                  gridTemplateColumns={'0.5fr 1fr'}
                >
                  <Box flexGrow={1}>
                    {/* Quantity */}
                    <NumericInput
                      inputProps={{
                        value: selectedItem?.quantity,
                        textAlign: 'center',
                      }}
                      onValueChange={(item: any) => {
                        updateItem(
                          'quantity',
                          item.floatValue,
                          selectedItem?.itemId
                        );
                      }}
                    />
                  </Box>
                  <Box flexGrow={1}>
                    {/* Price */}
                    <NumericInput
                      inputProps={{
                        value: selectedItem?.price?.toFixed(2),
                        textAlign: 'center',
                        prefix: formatMoney(0, {
                          currencyCode: invoiceDetails?.currency_code,
                          showCurrencyCode: false,
                        })[0],
                      }}
                      onValueChange={(item: any) => {
                        updateItem(
                          'price',
                          item.floatValue,
                          selectedItem?.itemId
                        );
                      }}
                    />
                  </Box>
                </Box>

                <Box
                  alignItems={'center'}
                  display={'flex'}
                  gap={'1rem'}
                  justifyContent={'flex-end'}
                  h={'40px'}
                  minH={'40px'}
                  maxH={'40px'}
                >
                  {/* Amount */}
                  <Stack alignItems="flex-end" gap={1}>
                    <Text fontWeight="semibold">
                      {formatMoney(itemTotalAmount, {
                        currencyCode: invoiceDetails?.currency_code,
                        showCurrencyCode: false,
                      })}
                    </Text>
                  </Stack>

                  {/* Delete Item Icon */}
                  <Box>
                    <FiTrash2
                      cursor={'pointer'}
                      color={Colors?.ORANGE?.PRIMARY}
                      onClick={() => deleteItem(selectedItem?.itemId)}
                    />
                  </Box>
                </Box>
              </Grid>

              {/* taxes */}
              {selectedItem?.taxes &&
                selectedItem.taxes.length > 0 &&
                selectedItem?.taxes?.map((taxItem: any) => {
                  const taxAmount =
                    (Number(taxItem.value) / 100) *
                    (selectedItem?.price * selectedItem?.quantity);
                  const defaultOption = taxOptions?.find(
                    (item: any) =>
                      Number(item?.value?.id) === Number(taxItem?.id)
                  );

                  // console.log('defaultOption is ', defaultOption);
                  // console.log('taxOptions is ', taxOptions);
                  // console.log('taxItem is ', taxItem);

                  return (
                    <Grid
                      key={taxItem?.id}
                      templateColumns={{ base: '1fr', md: '3.5fr 1.5fr 1fr' }}
                      // templateColumns={'2fr 2fr 1fr 0.5fr auto'}
                      // _hover={{ bg: '#FDFEFF' }} // Lighter hover background
                      p={6}
                      py={'.5rem'}
                      borderBottom="1px solid #EDF1F3"
                      alignItems="center"
                      gap={4}
                    >
                      <Box></Box>

                      <Box alignItems={'center'}>
                        <CustomSelect
                          placeholder="Add a tax"
                          onChange={(e: any) => {
                            const isSameTax = selectedItem?.taxes?.find(
                              (item: any) => item.id === e?.value?.id
                            );
                            if (isSameTax) {
                              return;
                            }

                            //remove current one
                            const remove = selectedItem?.taxes?.filter(
                              (item: any) =>
                                item?.id !== defaultOption?.value?.id
                            );
                            updateItem(
                              'taxes',
                              [...(remove || []), e.value],
                              selectedItem?.itemId
                            );
                          }}
                          selectedOption={defaultOption}
                          options={taxOptions || []}
                          controlStyle={{
                            fontSize: '.75rem',
                            flex: 1,
                          }}
                          components={{
                            IndicatorSeparator: () => null,
                          }}
                        />{' '}
                      </Box>
                      <Box
                        alignItems={'center'}
                        display={'flex'}
                        gap={'1rem'}
                        justifyContent={'flex-end'}
                        h={'40px'}
                        minH={'40px'}
                        maxH={'40px'}
                      >
                        {itemTaxAmount > 0 && (
                          <Text fontSize="sm" color="gray.500">
                            {formatMoney(taxAmount, {
                              currencyCode: invoiceDetails?.currency_code,
                              showCurrencyCode: false,
                            })}
                          </Text>
                        )}
                        <FiTrash2
                          cursor={'pointer'}
                          color={Colors?.ORANGE?.PRIMARY}
                          size="12px"
                          onClick={() => {
                            const newTaxes = selectedItem.taxes.filter(
                              (item: any) => item.id !== taxItem?.id
                            );
                            updateItem('taxes', newTaxes, selectedItem?.itemId);
                          }}
                        />
                      </Box>
                    </Grid>
                  );
                })}

              <Grid
                templateColumns={{ base: '1fr', md: '3.5fr 1.5fr 1fr' }}
                // _hover={{ bg: '#FDFEFF' }} // Lighter hover background
                p={6}
                py={'.5rem'}
                borderBottom="1px solid #EDF1F3"
                alignItems="center"
                gap={4}
              >
                <Box></Box>

                <Box alignItems={'center'}>
                  <CustomSelect
                    placeholder="Add Tax"
                    onChange={(e: any) => {
                      if (e?.action === 'openTaxModal') {
                        actions.openTaxModal(selectedItem);
                        return;
                      }
                      const prevTax = selectedItem?.taxes || [];
                      const itExist = prevTax?.find(
                        (item: any) => item.id === e?.value?.id
                      );
                      if (itExist) {
                        return;
                      }
                      updateItem(
                        'taxes',
                        [...(selectedItem?.taxes || []), e.value],
                        selectedItem?.itemId
                      );
                    }}
                    options={[
                      ...(taxOptions || []),
                      {
                        label: (
                          <Flex alignItems="center" gap="0.5rem">
                            <FiPlus color={Colors?.ORANGE?.PRIMARY} />
                            <Text
                              color={Colors?.ORANGE?.PRIMARY}
                              fontWeight="bold"
                              fontSize={'.75rem'}
                            >
                              Add new tax
                            </Text>
                          </Flex>
                        ),
                        value: 'add-new-tax', // Dummy value for action
                        action: 'openTaxModal', // Custom action for modal
                      },
                    ]}
                    selectedOption={null} // Always null so placeholder shows
                    isClearable={true}
                    controlStyle={{
                      fontSize: '.75rem',
                    }}
                    optionStyle={{
                      fontSize: '.75rem',
                    }}
                    components={{
                      IndicatorSeparator: () => null,
                    }}
                  />
                </Box>

                <Box
                  alignItems={'center'}
                  display={'flex'}
                  gap={'1rem'}
                  justifyContent={'flex-end'}
                  h={'40px'}
                  minH={'40px'}
                  maxH={'40px'}
                >
                  {itemTaxAmount > 0 && (
                    <Text fontSize="sm" color="gray.500"></Text>
                  )}
                  <Box></Box>
                </Box>
              </Grid>
            </Box>
          );
        })}
      </Stack>
    </Box>
  );
}
